# -*- coding: utf-8 -*-
"""
坐标提取功能简单测试脚本

该脚本直接包含坐标提取的核心代码，避免复杂的依赖关系，
专门用于验证坐标提取算法的正确性。
"""

import re
import time
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from difflib import SequenceMatcher


@dataclass
class RowCoordinate:
    """行坐标数据模型"""
    row_index: int                          # 行索引（从0开始）
    y_min: float                           # 行的最小Y坐标
    y_max: float                           # 行的最大Y坐标
    x_min: float                           # 行的最小X坐标
    x_max: float                           # 行的最大X坐标
    center_y: float                        # 行的中心Y坐标
    height: float                          # 行高度
    width: float                           # 行宽度
    matched_blocks: List[Dict[str, Any]]   # 匹配到的OCR文本块
    confidence: float                      # 匹配置信度
    test_item_indices: List[int]           # 关联的测试项目索引


class SimpleTestItem:
    """简化的测试项目类"""
    def __init__(self, test_code="", test_name="", test_value="", test_unit="", reference_value=""):
        self.test_code = test_code
        self.test_name = test_name
        self.test_value = test_value
        self.test_unit = test_unit
        self.reference_value = reference_value


class CoordinateMapper:
    """坐标映射器：将结构化数据映射回OCR坐标"""
    
    def __init__(self, similarity_threshold: float = 0.6):
        self.similarity_threshold = similarity_threshold
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0.0
        
        # 清理文本：移除空格和特殊字符
        clean_text1 = re.sub(r'\s+', '', text1.lower())
        clean_text2 = re.sub(r'\s+', '', text2.lower())
        
        # 使用序列匹配器计算相似度
        return SequenceMatcher(None, clean_text1, clean_text2).ratio()
    
    def find_matching_blocks(self, target_text: str, ocr_blocks: List[Dict[str, Any]]) -> List[Tuple[int, float]]:
        """查找与目标文本匹配的OCR文本块"""
        matches = []
        
        for i, block in enumerate(ocr_blocks):
            block_text = block.get('words', '')
            similarity = self.calculate_text_similarity(target_text, block_text)
            
            if similarity >= self.similarity_threshold:
                matches.append((i, similarity))
        
        # 按相似度降序排序
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches
    
    def map_item_to_blocks(self, test_item, ocr_blocks: List[Dict[str, Any]]) -> List[int]:
        """将单个测试项目映射到OCR文本块"""
        matched_block_indices = []
        
        # 构建搜索文本：组合测试项目的关键字段
        search_texts = []
        
        if hasattr(test_item, 'test_code') and test_item.test_code:
            search_texts.append(test_item.test_code)
        if hasattr(test_item, 'test_name') and test_item.test_name:
            search_texts.append(test_item.test_name)
        if hasattr(test_item, 'test_value') and test_item.test_value:
            search_texts.append(test_item.test_value)
        if hasattr(test_item, 'test_unit') and test_item.test_unit:
            search_texts.append(test_item.test_unit)
        
        # 为每个搜索文本查找匹配的块
        for search_text in search_texts:
            matches = self.find_matching_blocks(search_text, ocr_blocks)
            for block_idx, similarity in matches:
                if block_idx not in matched_block_indices:
                    matched_block_indices.append(block_idx)
        
        return matched_block_indices


class RowCoordinateExtractor:
    """行坐标提取器：从OCR坐标中提取行级别的坐标信息"""
    
    def __init__(self, y_tolerance: float = 10.0):
        self.y_tolerance = y_tolerance
    
    def get_block_center_y(self, location: List[List[float]]) -> float:
        """计算文本块的中心Y坐标"""
        if not location or len(location) != 4:
            return 0.0
        
        y_coords = [point[1] for point in location]
        return sum(y_coords) / len(y_coords)
    
    def get_block_bounds(self, location: List[List[float]]) -> Tuple[float, float, float, float]:
        """计算文本块的边界坐标"""
        if not location or len(location) != 4:
            return 0.0, 0.0, 0.0, 0.0
        
        x_coords = [point[0] for point in location]
        y_coords = [point[1] for point in location]
        
        return min(x_coords), min(y_coords), max(x_coords), max(y_coords)
    
    def group_blocks_by_row(self, block_indices: List[int], ocr_blocks: List[Dict[str, Any]]) -> List[List[int]]:
        """根据Y坐标将文本块分组为行"""
        if not block_indices:
            return []
        
        # 计算每个块的中心Y坐标
        blocks_with_y = []
        for idx in block_indices:
            if idx < len(ocr_blocks):
                block = ocr_blocks[idx]
                location = block.get('location', [])
                center_y = self.get_block_center_y(location)
                blocks_with_y.append((idx, center_y))
        
        # 按Y坐标排序
        blocks_with_y.sort(key=lambda x: x[1])
        
        # 分组：Y坐标相近的块归为同一行
        rows = []
        current_row = []
        current_y = None
        
        for block_idx, center_y in blocks_with_y:
            if current_y is None or abs(center_y - current_y) <= self.y_tolerance:
                # 同一行
                current_row.append(block_idx)
                current_y = center_y if current_y is None else (current_y + center_y) / 2
            else:
                # 新行
                if current_row:
                    rows.append(current_row)
                current_row = [block_idx]
                current_y = center_y
        
        # 添加最后一行
        if current_row:
            rows.append(current_row)
        
        return rows
    
    def calculate_row_coordinates(self, row_block_indices: List[int], 
                                ocr_blocks: List[Dict[str, Any]], 
                                row_index: int,
                                test_item_indices: List[int] = None) -> RowCoordinate:
        """计算行的坐标信息"""
        if not row_block_indices:
            return RowCoordinate(
                row_index=row_index,
                y_min=0.0, y_max=0.0, x_min=0.0, x_max=0.0,
                center_y=0.0, height=0.0, width=0.0,
                matched_blocks=[], confidence=0.0,
                test_item_indices=test_item_indices or []
            )
        
        # 收集所有块的坐标和信息
        all_x_coords = []
        all_y_coords = []
        matched_blocks = []
        total_confidence = 0.0
        
        for block_idx in row_block_indices:
            if block_idx < len(ocr_blocks):
                block = ocr_blocks[block_idx]
                location = block.get('location', [])
                confidence = block.get('confidence', 1.0)
                
                if location and len(location) == 4:
                    # 提取所有坐标点
                    for point in location:
                        all_x_coords.append(point[0])
                        all_y_coords.append(point[1])
                    
                    matched_blocks.append(block)
                    total_confidence += confidence
        
        if not all_x_coords or not all_y_coords:
            return RowCoordinate(
                row_index=row_index,
                y_min=0.0, y_max=0.0, x_min=0.0, x_max=0.0,
                center_y=0.0, height=0.0, width=0.0,
                matched_blocks=[], confidence=0.0,
                test_item_indices=test_item_indices or []
            )
        
        # 计算边界坐标
        x_min, x_max = min(all_x_coords), max(all_x_coords)
        y_min, y_max = min(all_y_coords), max(all_y_coords)
        center_y = (y_min + y_max) / 2
        height = y_max - y_min
        width = x_max - x_min
        
        # 计算平均置信度
        avg_confidence = total_confidence / len(matched_blocks) if matched_blocks else 0.0
        
        return RowCoordinate(
            row_index=row_index,
            y_min=y_min, y_max=y_max, x_min=x_min, x_max=x_max,
            center_y=center_y, height=height, width=width,
            matched_blocks=matched_blocks, confidence=avg_confidence,
            test_item_indices=test_item_indices or []
        )


def extract_row_coordinates_simple(test_items: List[Any], ocr_blocks: List[Dict[str, Any]], 
                                 similarity_threshold: float = 0.6, 
                                 y_tolerance: float = 10.0) -> List[RowCoordinate]:
    """简化版的行坐标提取函数"""
    if not test_items or not ocr_blocks:
        return []
    
    # 第一步：将测试项目映射到OCR文本块
    mapper = CoordinateMapper(similarity_threshold=similarity_threshold)
    item_to_blocks = {}
    
    for i, test_item in enumerate(test_items):
        matched_blocks = mapper.map_item_to_blocks(test_item, ocr_blocks)
        if matched_blocks:
            item_to_blocks[i] = matched_blocks
    
    if not item_to_blocks:
        return []
    
    # 第二步：收集所有匹配的块索引
    all_matched_blocks = set()
    for block_indices in item_to_blocks.values():
        all_matched_blocks.update(block_indices)
    
    # 第三步：按行分组OCR块
    extractor = RowCoordinateExtractor(y_tolerance=y_tolerance)
    row_groups = extractor.group_blocks_by_row(list(all_matched_blocks), ocr_blocks)
    
    # 第四步：为每行计算坐标信息
    row_coordinates = []
    
    for row_idx, row_block_indices in enumerate(row_groups):
        # 找到该行关联的测试项目
        associated_items = []
        for item_idx, block_indices in item_to_blocks.items():
            # 检查是否有交集
            if set(block_indices) & set(row_block_indices):
                associated_items.append(item_idx)
        
        # 计算行坐标
        row_coord = extractor.calculate_row_coordinates(
            row_block_indices, ocr_blocks, row_idx, associated_items
        )
        
        row_coordinates.append(row_coord)
    
    # 按Y坐标排序（从上到下）
    row_coordinates.sort(key=lambda x: x.center_y)
    
    # 重新分配行索引
    for i, row_coord in enumerate(row_coordinates):
        row_coord.row_index = i
    
    return row_coordinates


def create_test_data():
    """创建测试数据"""
    # 模拟测试项目
    test_items = [
        SimpleTestItem(
            test_code="GLU",
            test_name="葡萄糖",
            test_value="4.90",
            test_unit="mmol/L",
            reference_value="3.90-6.10"
        ),
        SimpleTestItem(
            test_code="CHOL",
            test_name="胆固醇",
            test_value="5.2",
            test_unit="mmol/L",
            reference_value="3.0-5.0"
        ),
        SimpleTestItem(
            test_code="TG",
            test_name="甘油三酯",
            test_value="1.8",
            test_unit="mmol/L",
            reference_value="0.5-1.7"
        )
    ]
    
    # 模拟OCR文本块（三行数据）
    ocr_blocks = [
        # 第一行：葡萄糖
        {"words": "GLU", "location": [[50, 200], [80, 200], [80, 220], [50, 220]], "confidence": 0.95},
        {"words": "葡萄糖", "location": [[100, 200], [150, 200], [150, 220], [100, 220]], "confidence": 0.95},
        {"words": "4.90", "location": [[200, 205], [230, 205], [230, 225], [200, 225]], "confidence": 0.98},
        {"words": "mmol/L", "location": [[300, 200], [350, 200], [350, 220], [300, 220]], "confidence": 0.90},
        {"words": "3.90-6.10", "location": [[400, 200], [470, 200], [470, 220], [400, 220]], "confidence": 0.92},
        
        # 第二行：胆固醇
        {"words": "CHOL", "location": [[50, 250], [85, 250], [85, 270], [50, 270]], "confidence": 0.93},
        {"words": "胆固醇", "location": [[100, 250], [150, 250], [150, 270], [100, 270]], "confidence": 0.93},
        {"words": "5.2", "location": [[200, 255], [220, 255], [220, 275], [200, 275]], "confidence": 0.97},
        {"words": "mmol/L", "location": [[300, 250], [350, 250], [350, 270], [300, 270]], "confidence": 0.90},
        {"words": "3.0-5.0", "location": [[400, 250], [450, 250], [450, 270], [400, 270]], "confidence": 0.91},
        
        # 第三行：甘油三酯
        {"words": "TG", "location": [[50, 300], [70, 300], [70, 320], [50, 320]], "confidence": 0.94},
        {"words": "甘油三酯", "location": [[100, 300], [160, 300], [160, 320], [100, 320]], "confidence": 0.96},
        {"words": "1.8", "location": [[200, 305], [220, 305], [220, 325], [200, 325]], "confidence": 0.99},
        {"words": "mmol/L", "location": [[300, 300], [350, 300], [350, 320], [300, 320]], "confidence": 0.90},
        {"words": "0.5-1.7", "location": [[400, 300], [440, 300], [440, 320], [400, 320]], "confidence": 0.88}
    ]
    
    return test_items, ocr_blocks


def test_coordinate_extraction():
    """测试坐标提取功能"""
    print("🚀 坐标提取功能测试")
    print("=" * 60)
    
    # 创建测试数据
    test_items, ocr_blocks = create_test_data()
    
    print("📝 测试数据:")
    print(f"  测试项目数: {len(test_items)}")
    for i, item in enumerate(test_items):
        print(f"    {i}: {item.test_name} = {item.test_value}")
    
    print(f"  OCR文本块数: {len(ocr_blocks)}")
    
    # 执行坐标提取
    print("\n🔍 执行坐标提取...")
    row_coordinates = extract_row_coordinates_simple(test_items, ocr_blocks)
    
    print(f"✅ 提取完成，识别到 {len(row_coordinates)} 行")
    
    # 验证结果
    assert len(row_coordinates) == 3, f"应该识别到3行，实际识别到{len(row_coordinates)}行"
    
    # 显示详细结果
    print("\n📍 行坐标详情:")
    print("-" * 60)
    for i, row in enumerate(row_coordinates):
        print(f"行 {i}:")
        print(f"  Y坐标范围: {row.y_min:.1f} - {row.y_max:.1f} (中心: {row.center_y:.1f})")
        print(f"  X坐标范围: {row.x_min:.1f} - {row.x_max:.1f}")
        print(f"  尺寸: {row.width:.1f} x {row.height:.1f}")
        print(f"  置信度: {row.confidence:.2f}")
        print(f"  匹配块数: {len(row.matched_blocks)}")
        print(f"  关联项目: {row.test_item_indices}")
        
        # 显示匹配的文本
        matched_texts = [block["words"] for block in row.matched_blocks]
        print(f"  匹配文本: {', '.join(matched_texts)}")
        print()
    
    # 验证行按Y坐标排序
    for i in range(1, len(row_coordinates)):
        assert row_coordinates[i-1].center_y <= row_coordinates[i].center_y, "行应该按Y坐标排序"
    
    # 验证每行都有关联的测试项目
    for row in row_coordinates:
        assert len(row.test_item_indices) > 0, "每行都应该有关联的测试项目"
    
    print("✅ 所有验证通过！")
    
    # 显示Y坐标列表
    print("📏 Y坐标范围列表:")
    for i, row in enumerate(row_coordinates):
        print(f"  行 {i}: {row.y_min:.1f} - {row.y_max:.1f}")
    
    # 显示边界框
    print("\n📦 边界框列表:")
    for i, row in enumerate(row_coordinates):
        print(f"  行 {i}: ({row.x_min:.1f}, {row.y_min:.1f}) - ({row.x_max:.1f}, {row.y_max:.1f})")
    
    print("\n🎯 坐标提取功能测试完成！")


if __name__ == "__main__":
    test_coordinate_extraction()
