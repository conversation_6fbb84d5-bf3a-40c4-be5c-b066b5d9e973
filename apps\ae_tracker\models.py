from django.db import models
from common.models import BaseModel


class TestOcrResult(BaseModel):
    """
    ocr结果
    """
    subject_id = models.CharField(max_length=50, verbose_name="受试者ID")
    subject_item_id = models.CharField(max_length=100, verbose_name="受试者操作项唯一标识（OT受试者访视数据源ID + 受试者ID）", db_index=True)
    ocr_text = models.TextField(null=True, blank=True, verbose_name="OCR 文本")

    class Meta:
        db_table = 'test_ocr_result'
        verbose_name = "AE ocr文本"
        verbose_name_plural = verbose_name


class TestResult(BaseModel):
    """
    化验结果
    """
    test_type = models.Char<PERSON>ield(max_length=50, null=True, blank=True, verbose_name="检查结果值类型")
    test_code = models.CharField(max_length=100, null=True, blank=True, verbose_name="检查代码")
    test_name = models.Char<PERSON>ield(max_length=100, null=True, blank=True, verbose_name="检查名称")
    ai_test_result_list = models.JSONField(null=True, blank=True, verbose_name="多个AI判断的异常结果提取")
    test_unit = models.CharField(max_length=20, null=True, blank=True, verbose_name="检查单位")
    test_value = models.CharField(max_length=100, null=True, blank=True, verbose_name="检查结果值")
    test_flag = models.SmallIntegerField(default=0, null=True, blank=True, verbose_name="检查结果标志；0：正常，1：偏高，2：偏低")
    abnormal_flag = models.SmallIntegerField(default=0, verbose_name="是否为异常值；0：正常， 1：异常")

    reference_value = models.CharField(max_length=20, null=True, blank=True, verbose_name="参考值")
    reference_range_min = models.CharField(max_length=20, null=True, blank=True, verbose_name="参考范围最小值")
    reference_range_max = models.CharField(max_length=20, null=True, blank=True, verbose_name="参考范围最大值")
    abnormal_symbol = models.CharField(max_length=20, null=True, blank=True, verbose_name="检查结果标志文本")
    test_text = models.CharField(max_length=255, null=True, blank=True, verbose_name="检查名称对应的所有文本")

    collect_time = models.DateTimeField(null=True, blank=True, verbose_name="采集时间")
    report_time = models.DateTimeField(null=True, blank=True, verbose_name="报告时间")
    ae_name = models.CharField(max_length=50, null=True, blank=True, verbose_name="不良事件名称")
    ae_grade = models.CharField(max_length=50, null=True, blank=True, verbose_name="判定CS等级:数据字典 AE_GRADE_CODE")
    ae_desc = models.CharField(max_length=255, null=True, blank=True, verbose_name="检查结果/AE事件描述")
    ae_edit_flag = models.CharField(max_length=20, null=True, blank=True, verbose_name="是否编辑过AE信息：0-否 1-是")
    # [{"model": "", "ae_name": "", "ae_grade": "", "ae_desc": ""}]
    ae_ai_result_list = models.JSONField(null=True, blank=True, verbose_name="多个AI判断的结果")
    ae_ai_result_flag = models.SmallIntegerField(null=True, blank=True, verbose_name="表示AI结果是否不一致；0：一致，1：不一致，2：已确认")
    medical_history_flag = models.SmallIntegerField(null=True, blank=True, verbose_name="是否病史标识：0-否 1-是")
    ae_tracker_flag = models.SmallIntegerField(default=0, blank=True, verbose_name="是否ae_tracker：0-否 1-是")
    ae_meds = models.CharField(max_length=500, null=True, blank=True, verbose_name="用药的结果")
    meds_edit_flag = models.CharField(max_length=20, null=True, blank=True, verbose_name="是否编辑过用药信息：0-否 1-是")
    ae_medication_measures_list = models.JSONField(null=True, blank=True, verbose_name="多个AI用药的结果")
    seq = models.FloatField(verbose_name="中间插入跟随排序", null=True, blank=True)
    page_num = models.IntegerField(null=True, blank=True, verbose_name="页面ID")

    project = models.ForeignKey(
        'project.Project',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='test_results',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )
    
    project_site = models.ForeignKey(
        'project.ProjectSite',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='test_results',
        to_field='project_site_id',
        db_column='project_site_id',
        verbose_name="项目中心ID",
        db_index=True
    )

    subject = models.ForeignKey(
        'subject.Subject',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='test_results',
        to_field='subject_id',
        db_column='subject_id',
        verbose_name="受试者ID",
        db_index=True
    )

    patient = models.ForeignKey(
        'patient.Patient',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='test_results',
        to_field='patient_id',
        db_column='patient_id',
        verbose_name="患者ID",
        db_index=True
    )

    subject_epoch = models.ForeignKey(
        'subject.SubjectEpoch',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='test_results',
        to_field='subject_epoch_id',
        db_column='subject_epoch_id',
        verbose_name="受试者阶段ID）",
        db_index=True
    )

    subject_visit = models.ForeignKey(
        'subject.SubjectVisit',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='test_results',
        to_field='subject_visit_id',
        db_column='subject_visit_id',
        verbose_name="受试者访视ID）",
        db_index=True
    )
    
    subject_item = models.ForeignKey(
        'subject.SubjectItem',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='test_results',
        to_field='subject_item_id',
        db_column='subject_item_id',
        verbose_name="受试者操作项ID）",
        db_index=True
    )

    subject_medical_info = models.ForeignKey(
        'subject_medical.SubjectMedicalInfo',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='test_results',
        to_field='id',
        db_column='subject_medical_info_id',
        verbose_name="医疗信息ID",
        db_index=True
    )
    
    class Meta:
        db_table = 'test_result'
        verbose_name = "化验结果"
        verbose_name_plural = verbose_name


class AeTrackerTask(BaseModel):
    """
    AE Tracker任务
    """
    TODO = 'TODO'
    IN_PROGRESS = 'IN_PROGRESS'
    COMPLETED = 'COMPLETED'
    CANCELLED = 'CANCELLED'
    ERROR = 'ERROR'
    STATUS_CHOICES = [(TODO, '待办'), (IN_PROGRESS, '进行中'), (COMPLETED, '已完成'), (CANCELLED, '已取消'), (ERROR, '执行错误')]

    PIC_MASK = 'PIC_MASK'  # 打码
    OCR_EXTRACTION = 'OCR_EXTRACTION'  # ocr结构化提取
    OCR_EXTRACTION_TEXT = 'OCR_EXTRACTION_TEXT'  # 从非检验单ocr文本结构化提取
    AE_RECOGNITION = 'AE_RECOGNITION'  # ae识别
    AE_MEDICATION_MEASURES = 'AE_MEDICATION_MEASURES'  # ae用药
    CATEGORY_CHOICES = [(OCR_EXTRACTION, 'OCR_EXTRACTION'), (OCR_EXTRACTION_TEXT, 'OCR_EXTRACTION_TEXT'), (AE_RECOGNITION, 'AE_RECOGNITION'), (PIC_MASK, 'PIC_MASK'), (AE_MEDICATION_MEASURES, 'AE_MEDICATION_MEASURES')]

    name = models.CharField(max_length=255, verbose_name="任务名称")
    category = models.CharField(max_length=50, choices=CATEGORY_CHOICES,
                                default=OCR_EXTRACTION, verbose_name="任务分类")
    description = models.TextField(blank=True, null=True, verbose_name="任务描述")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=TODO, verbose_name="任务状态")
    start_time = models.DateTimeField(auto_now_add=True, verbose_name="开始时间")
    end_time = models.DateTimeField(blank=True, null=True, verbose_name="结束时间")
    
    project = models.ForeignKey(
        'project.Project',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='ae_tracker_tasks',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )
    
    project_site = models.ForeignKey(
        'project.ProjectSite',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='ae_tracker_tasks',
        to_field='project_site_id',
        db_column='project_site_id',
        verbose_name="项目中心ID",
        db_index=True
    )

    subject = models.ForeignKey(
        'subject.Subject',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='ae_tracker_tasks',
        to_field='subject_id',
        db_column='subject_id',
        verbose_name="受试者ID",
        db_index=True
    )

    patient = models.ForeignKey(
        'patient.Patient',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='ae_tracker_tasks',
        to_field='patient_id',
        db_column='patient_id',
        verbose_name="患者ID",
        db_index=True
    )

    subject_epoch = models.ForeignKey(
        'subject.SubjectEpoch',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='ae_tracker_tasks',
        to_field='subject_epoch_id',
        db_column='subject_epoch_id',
        verbose_name="受试者阶段ID）",
        db_index=True
    )

    subject_visit = models.ForeignKey(
        'subject.SubjectVisit',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='ae_tracker_tasks',
        to_field='subject_visit_id',
        db_column='subject_visit_id',
        verbose_name="受试者访视ID）",
        db_index=True
    )
    
    subject_item = models.ForeignKey(
        'subject.SubjectItem',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='ae_tracker_tasks',
        to_field='subject_item_id',
        db_column='subject_item_id',
        verbose_name="受试者操作项ID）",
        db_index=True
    )
    
    class Meta:
        db_table = 'ae_tracker_task'
        verbose_name = "AE Tracker任务"
        verbose_name_plural = verbose_name
