"""
OCR请求工具类：提供Paddle OCR服务调用接口
基于script/test_result_format.py中的OCR实现

功能特性：
- 支持单个文件和目录批量处理
- 自动检测输入类型（文件/目录）和文件格式
- 支持PDF自动分割为图片进行OCR处理
- 支持常见图片格式（PNG, JPG, JPEG, BMP, WEBP等）
- 完善的错误处理和日志记录
"""

import os
import json
import ast
import base64
import requests
import time
import logging
import tempfile
import shutil
from pathlib import Path
from typing import Union, Optional, Dict, Any, List, Tuple
import urllib3

# PDF处理相关导入
try:
    import fitz  # PyMuPDF
    PDF_SUPPORT = True
except ImportError:
    PDF_SUPPORT = False

# 图片处理相关导入
try:
    from PIL import Image
    PIL_SUPPORT = True
except ImportError:
    PIL_SUPPORT = False

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OCRClient:
    """OCR客户端类，提供Paddle OCR服务调用接口"""

    # 支持的图片格式
    SUPPORTED_IMAGE_FORMATS = {'.png', '.jpg', '.jpeg', '.bmp', '.webp', '.tiff', '.tif', '.gif'}

    # 支持的PDF格式
    SUPPORTED_PDF_FORMATS = {'.pdf'}

    def __init__(self,
                 ocr_url: str = "http://192.168.230.3:8011/ocr/single",
                 temp_dir: Optional[str] = None,
                 pdf_dpi: int = 200,
                 pdf_image_format: str = 'png'
          ):
        """
        初始化OCR客户端

        Args:
            ocr_url: Paddle OCR服务URL
            temp_dir: 临时文件目录，用于存储PDF转换的图片
            pdf_dpi: PDF转图片的DPI设置，默认200
            pdf_image_format: PDF转换图片的格式，默认png
        """
        self.ocr_url = ocr_url
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.pdf_dpi = pdf_dpi
        self.pdf_image_format = pdf_image_format

        # 设置请求头
        self.headers = {
            'Content-Type': 'application/json',
        }

        # 检查PDF支持
        if not PDF_SUPPORT:
            logger.warning("PDF处理功能不可用，请安装PyMuPDF: pip install PyMuPDF")

    def detect_input_type(self, input_path: str) -> Tuple[str, str]:
        """
        检测输入类型和文件格式

        Args:
            input_path: 输入路径

        Returns:
            (input_type, file_format) - 输入类型('file'/'directory')和文件格式

        Raises:
            FileNotFoundError: 当路径不存在时
            ValueError: 当输入类型不支持时
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"路径不存在: {input_path}")

        if os.path.isfile(input_path):
            file_ext = Path(input_path).suffix.lower()
            if file_ext in self.SUPPORTED_IMAGE_FORMATS:
                return 'file', 'image'
            elif file_ext in self.SUPPORTED_PDF_FORMATS:
                return 'file', 'pdf'
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")
        elif os.path.isdir(input_path):
            return 'directory', 'mixed'
        else:
            raise ValueError(f"不支持的输入类型: {input_path}")

    def is_supported_file(self, filename: str) -> Tuple[bool, str]:
        """
        检查文件是否为支持的格式

        Args:
            filename: 文件名

        Returns:
            (is_supported, file_type) - 是否支持和文件类型
        """
        file_ext = Path(filename).suffix.lower()
        if file_ext in self.SUPPORTED_IMAGE_FORMATS:
            return True, 'image'
        elif file_ext in self.SUPPORTED_PDF_FORMATS:
            return True, 'pdf'
        else:
            return False, 'unknown'

    def pdf_to_images(self, pdf_path: str) -> List[str]:
        """
        将PDF文件转换为图片文件

        Args:
            pdf_path: PDF文件路径

        Returns:
            转换后的图片文件路径列表

        Raises:
            ImportError: 当PyMuPDF未安装时
            Exception: 当PDF处理失败时
        """
        if not PDF_SUPPORT:
            raise ImportError("PDF处理功能不可用，请安装PyMuPDF: pip install PyMuPDF")

        try:
            # 创建临时目录
            pdf_name = Path(pdf_path).stem
            temp_pdf_dir = os.path.join(self.temp_dir, f"pdf_images_{pdf_name}_{int(time.time())}")
            os.makedirs(temp_pdf_dir, exist_ok=True)

            # 打开PDF文档
            doc = fitz.open(pdf_path)
            image_paths = []

            logger.info(f"开始处理PDF文件: {pdf_path}，共{len(doc)}页")

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)

                # 设置缩放矩阵以控制DPI
                zoom = self.pdf_dpi / 72.0  # 72是PDF的默认DPI
                mat = fitz.Matrix(zoom, zoom)

                # 渲染页面为图片
                pix = page.get_pixmap(matrix=mat)

                # 保存图片
                image_filename = f"page_{page_num + 1:03d}.{self.pdf_image_format}"
                image_path = os.path.join(temp_pdf_dir, image_filename)
                pix.save(image_path)
                image_paths.append(image_path)

                logger.debug(f"已转换第{page_num + 1}页: {image_filename}")

            doc.close()
            logger.info(f"PDF转换完成，生成{len(image_paths)}张图片")
            return image_paths

        except Exception as e:
            logger.error(f"PDF转换失败: {e}")
            raise Exception(f"PDF处理失败: {e}")

    def cleanup_temp_files(self, temp_dir: str):
        """
        清理临时文件

        Args:
            temp_dir: 临时目录路径
        """
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                logger.debug(f"已清理临时目录: {temp_dir}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")

    def get_base64(self, data: Union[str, bytes]) -> str:
        """
        将文件路径或字节数据转换为base64编码
        
        Args:
            data: 文件路径(str)或字节数据(bytes)
            
        Returns:
            base64编码的字符串
            
        Raises:
            ValueError: 当数据类型不支持时
        """
        if isinstance(data, str):
            # 如果传入的是文件路径
            with open(data, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode()
        elif isinstance(data, bytes):
            # 如果传入的是字节流
            encoded_string = base64.b64encode(data).decode()
        else:
            raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")
        return encoded_string

    def get_ocr_result(self, url: str, payload: Union[str, dict], headers: dict) -> str:
        """
        通过API获取OCR结果（已弃用，请使用统一OCR服务）
        
        Args:
            url: OCR服务URL
            payload: 请求载荷
            headers: 请求头
            
        Returns:
            OCR响应文本
            
        Raises:
            Exception: 当请求失败时
        """
        logger.warning("get_ocr_result方法已弃用，请使用统一OCR服务")
        try:
            start_time = time.time()
            response = requests.post(url, headers=headers, data=payload, verify=False)
            end_time = time.time()
            
            logger.info(f"OCR API调用耗时: {end_time - start_time:.2f}秒")
            
            if response.status_code == 200:
                return response.text
            else:
                raise Exception(f"OCR API调用失败，状态码：{response.status_code}, 响应：{response.text}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"OCR网络请求异常: {e}")

    def get_keywords(self, ocr_result: str) -> str:
        """
        提取OCR结果中的文本内容
        
        Args:
            ocr_result: OCR API返回的原始结果
            
        Returns:
            提取的文本内容
        """
        text = ""
        try:
            ocr_dict = ast.literal_eval(ocr_result)
            text_list = []
            for result in ocr_dict["result"]["words_block_list"]:
                text_list.append(result["words"])
                text += result["words"] + "\n"
            logger.debug(f"提取的文本块: {text_list}")
        except Exception as e:
            logger.error(f"❌ OCR结果解析失败: {e}")
        return text.strip()

    def process_input(self, input_path: str, cleanup_temp: bool = True) -> str:
        """
        智能处理输入（自动检测类型并处理）

        Args:
            input_path: 输入路径（文件或目录）
            cleanup_temp: 是否清理临时文件，默认True

        Returns:
            OCR识别结果文本

        Raises:
            FileNotFoundError: 当路径不存在时
            ValueError: 当输入类型不支持时
        """
        try:
            input_type, file_format = self.detect_input_type(input_path)
            logger.info(f"检测到输入类型: {input_type}, 格式: {file_format}")

            if input_type == 'file':
                if file_format == 'image':
                    return self.ocr_main(input_path)
                elif file_format == 'pdf':
                    return self.process_pdf_file(input_path, cleanup_temp)
            elif input_type == 'directory':
                return self.process_directory(input_path, cleanup_temp)
            else:
                raise ValueError(f"不支持的输入类型: {input_type}")

        except Exception as e:
            logger.error(f"处理输入失败: {e}")
            raise

    def process_pdf_file(self, pdf_path: str, cleanup_temp: bool = True) -> str:
        """
        处理PDF文件

        Args:
            pdf_path: PDF文件路径
            cleanup_temp: 是否清理临时文件

        Returns:
            OCR识别结果文本
        """
        temp_dirs = []
        try:
            logger.info(f"开始处理PDF文件: {pdf_path}")

            # 将PDF转换为图片
            image_paths = self.pdf_to_images(pdf_path)
            if image_paths:
                temp_dirs.append(os.path.dirname(image_paths[0]))

            # 对每张图片进行OCR
            ocr_results = []
            for i, image_path in enumerate(image_paths, 1):
                logger.info(f"正在处理第{i}/{len(image_paths)}页...")
                ocr_result = self.ocr_main(image_path)
                if ocr_result.strip():
                    ocr_results.append(f"## 第{i}页\n{ocr_result}\n")

            logger.info(f"PDF处理完成，共处理{len(image_paths)}页")
            return "\n".join(ocr_results)

        except Exception as e:
            logger.error(f"PDF文件处理失败: {e}")
            raise
        finally:
            # 清理临时文件
            if cleanup_temp:
                for temp_dir in temp_dirs:
                    self.cleanup_temp_files(temp_dir)

    def process_directory(self, directory_path: str, cleanup_temp: bool = True) -> str:
        """
        处理目录中的所有支持文件

        Args:
            directory_path: 目录路径
            cleanup_temp: 是否清理临时文件

        Returns:
            合并的OCR识别结果文本
        """
        try:
            logger.info(f"开始处理目录: {directory_path}")

            if not os.path.exists(directory_path):
                raise FileNotFoundError(f"目录不存在: {directory_path}")

            ocr_results = []
            temp_dirs = []
            processed_count = 0

            # 获取所有文件并排序
            all_files = []
            for filename in os.listdir(directory_path):
                file_path = os.path.join(directory_path, filename)
                if os.path.isfile(file_path):
                    is_supported, file_type = self.is_supported_file(filename)
                    if is_supported:
                        all_files.append((file_path, filename, file_type))

            all_files.sort(key=lambda x: x[1])  # 按文件名排序

            logger.info(f"找到{len(all_files)}个支持的文件")

            for file_path, filename, file_type in all_files:
                try:
                    logger.info(f"正在处理文件: {filename} (类型: {file_type})")

                    if file_type == 'image':
                        ocr_result = self.ocr_main(file_path)
                        if ocr_result.strip():
                            ocr_results.append(f"## {filename}\n{ocr_result}\n")
                            processed_count += 1

                    elif file_type == 'pdf':
                        # 处理PDF文件
                        image_paths = self.pdf_to_images(file_path)
                        if image_paths:
                            temp_dirs.append(os.path.dirname(image_paths[0]))

                        pdf_results = []
                        for i, image_path in enumerate(image_paths, 1):
                            ocr_result = self.ocr_main(image_path)
                            if ocr_result.strip():
                                pdf_results.append(f"### 第{i}页\n{ocr_result}\n")

                        if pdf_results:
                            ocr_results.append(f"## {filename}\n" + "\n".join(pdf_results))
                            processed_count += 1

                except Exception as e:
                    logger.error(f"处理文件{filename}失败: {e}")
                    continue

            logger.info(f"目录处理完成，成功处理{processed_count}个文件")
            return "\n".join(ocr_results)

        except Exception as e:
            logger.error(f"目录处理失败: {e}")
            raise
        finally:
            # 清理临时文件
            if cleanup_temp:
                for temp_dir in temp_dirs:
                    self.cleanup_temp_files(temp_dir)

    def ocr_main_with_coordinates(self, img_data: Union[str, bytes],
                                language: str = "zh",
                                detect_direction: bool = True,
                                quick_mode: bool = False,
                                single_orientation_mode: bool = True) -> Dict[str, Any]:
        """
        执行图片OCR识别并返回文本内容和坐标信息

        Args:
            img_data: 图片文件路径或字节数据
            language: 识别语言，默认中文（统一OCR服务固定为中文）
            detect_direction: 检测方向，映射到统一OCR服务的use_correction参数
            quick_mode: 快速模式（统一OCR服务暂不支持）
            single_orientation_mode: 单一方向模式（统一OCR服务暂不支持）

        Returns:
            包含文本和坐标信息的字典
            {
                "text": str,  # 识别到的markdown格式文本
                "ocr_blocks": List[Dict],  # OCR文本块列表（包含坐标）
                "ocr_result": Dict  # 完整的OCR结果
            }
        """
        try:
            # 处理图片数据
            if isinstance(img_data, str):
                # 如果传入的是文件路径
                with open(img_data, "rb") as image_file:
                    image_bytes = image_file.read()
            elif isinstance(img_data, bytes):
                # 如果传入的是字节流
                image_bytes = img_data
            else:
                raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")

            # 使用统一OCR服务，映射detect_direction参数到use_correction
            use_correction = detect_direction  # 映射参数
            from common.clients.ocr_service import process_ocr
            result = process_ocr(image_bytes, use_correction=use_correction)

            # 提取文本和坐标信息
            text = result.get("markdown_text", "")
            ocr_blocks = result.get("text_blocks", [])
            ocr_result = result.get("ocr_result", {})

            print(f"📝 提取的文本长度: {len(text)}, OCR块数: {len(ocr_blocks)}")

            return {
                "text": text.strip(),
                "ocr_blocks": ocr_blocks,
                "ocr_result": ocr_result
            }

        except Exception as e:
            logger.error(f"❌ 图片OCR识别失败: {e}")
            return {
                "text": "",
                "ocr_blocks": [],
                "ocr_result": {}
            }

    def ocr_main(self, img_data: Union[str, bytes],
                 language: str = "zh",
                 detect_direction: bool = True,
                 quick_mode: bool = False,
                 single_orientation_mode: bool = True) -> str:
        """
        执行图片OCR识别并返回识别到的文本内容

        Args:
            img_data: 图片文件路径或字节数据
            language: 识别语言，默认中文（统一OCR服务固定为中文）
            detect_direction: 检测方向，映射到统一OCR服务的use_correction参数
            quick_mode: 快速模式（统一OCR服务暂不支持）
            single_orientation_mode: 单一方向模式（统一OCR服务暂不支持）

        Returns:
            识别到的markdown格式文本
        """
        try:
            # 处理图片数据
            if isinstance(img_data, str):
                # 如果传入的是文件路径
                with open(img_data, "rb") as image_file:
                    image_bytes = image_file.read()
            elif isinstance(img_data, bytes):
                # 如果传入的是字节流
                image_bytes = img_data
            else:
                raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")
            
            # 使用统一OCR服务，映射detect_direction参数到use_correction
            use_correction = detect_direction  # 映射参数
            from common.clients.ocr_service import process_ocr
            result = process_ocr(image_bytes, use_correction=use_correction)
            
            # 直接使用统一OCR服务返回的markdown_text作为完整文本
            text = result.get("markdown_text", "")
            
            print(f"📝 提取的文本长度: {len(text)}")
            return text.strip()
            
        except Exception as e:
            logger.error(f"❌ 图片OCR识别失败: {e}")
            return ""

    def process_images_in_directory(self, images_directory: str) -> str:
        """
        遍历指定文件夹中的所有图片文件并进行OCR识别
        （保持向后兼容，建议使用 process_directory 方法）

        Args:
            images_directory: 图片文件夹路径

        Returns:
            合并后的OCR文本结果
        """
        logger.warning("process_images_in_directory方法已过时，建议使用process_directory方法")

        ocr_texts = []
        logger.info("🔍 开始进行批量OCR识别...")

        if not os.path.exists(images_directory):
            logger.error(f"图片文件夹不存在: {images_directory}")
            return ""

        for filename in sorted(os.listdir(images_directory)):
            image_path = os.path.join(images_directory, filename)

            if self.is_supported_image_format(filename):
                try:
                    ocr_result = self.ocr_main(image_path)
                    char_count = len(ocr_result)
                    logger.info(f"📄 图片 {filename} 识别完成，字符数: {char_count}")

                    if ocr_result:
                        ocr_texts.append(f"## {filename}\n{ocr_result}\n")
                except Exception as e:
                    logger.error(f"处理图片{filename}失败: {e}")
                    continue

        logger.info("✅ 批量OCR识别全部完成！")
        return "\n".join(ocr_texts)

    def is_supported_image_format(self, filename: str) -> bool:
        """
        检查文件是否为支持的图片格式
        （保持向后兼容，建议使用 is_supported_file 方法）

        Args:
            filename: 文件名

        Returns:
            是否为支持的图片格式
        """
        file_ext = Path(filename).suffix.lower()
        return file_ext in self.SUPPORTED_IMAGE_FORMATS

    def benchmark_ocr_performance(self, input_path: str, cleanup_temp: bool = True) -> Dict[str, Any]:
        """
        对指定输入进行OCR性能基准测试

        Args:
            input_path: 输入路径（文件或目录）
            cleanup_temp: 是否清理临时文件，默认True

        Returns:
            包含性能统计数据的字典，包括:
            - total_files: 处理的总文件数
            - total_images: 处理的总图片数（包括PDF页面）
            - total_time: 总处理时间
            - avg_time_per_file: 平均每文件处理时间
            - avg_time_per_image: 平均每图片处理时间
            - per_file_stats: 每个文件的详细统计信息
            - per_image_stats: 每张图片的详细统计信息
            - summary: 性能总结
        """
        try:
            input_type, file_format = self.detect_input_type(input_path)
            logger.info(f"🔍 开始OCR性能基准测试: {input_path}")
            logger.info(f"输入类型: {input_type}, 格式: {file_format}")

            stats = {
                "input_path": input_path,
                "input_type": input_type,
                "file_format": file_format,
                "total_files": 0,
                "total_images": 0,
                "total_time": 0,
                "avg_time_per_file": 0,
                "avg_time_per_image": 0,
                "per_file_stats": [],
                "per_image_stats": [],
                "summary": {}
            }

            benchmark_start_time = time.time()
            temp_dirs = []

            # 根据输入类型处理
            if input_type == 'file':
                if file_format == 'image':
                    file_stats = self._benchmark_single_image(input_path)
                    stats["per_file_stats"].append(file_stats)
                    stats["per_image_stats"].extend(file_stats["image_stats"])
                    stats["total_files"] = 1
                    stats["total_images"] = 1
                elif file_format == 'pdf':
                    file_stats = self._benchmark_single_pdf(input_path, cleanup_temp)
                    if file_stats.get("temp_dir"):
                        temp_dirs.append(file_stats["temp_dir"])
                    stats["per_file_stats"].append(file_stats)
                    stats["per_image_stats"].extend(file_stats["image_stats"])
                    stats["total_files"] = 1
                    stats["total_images"] = len(file_stats["image_stats"])

            elif input_type == 'directory':
                dir_stats = self._benchmark_directory(input_path, cleanup_temp)
                stats["per_file_stats"] = dir_stats["file_stats"]
                stats["per_image_stats"] = dir_stats["image_stats"]
                stats["total_files"] = dir_stats["total_files"]
                stats["total_images"] = dir_stats["total_images"]
                temp_dirs.extend(dir_stats["temp_dirs"])

            # 计算总体统计
            total_benchmark_time = time.time() - benchmark_start_time
            stats["total_time"] = total_benchmark_time

            if stats["total_files"] > 0:
                stats["avg_time_per_file"] = total_benchmark_time / stats["total_files"]
            if stats["total_images"] > 0:
                stats["avg_time_per_image"] = total_benchmark_time / stats["total_images"]

            # 生成性能总结
            stats["summary"] = self._generate_performance_summary(stats)

            # 输出性能报告
            self._print_performance_report(stats)

            return stats

        except Exception as e:
            logger.error(f"性能基准测试失败: {e}")
            raise
        finally:
            # 清理临时文件
            if cleanup_temp:
                for temp_dir in temp_dirs:
                    self.cleanup_temp_files(temp_dir)

    def _benchmark_single_image(self, image_path: str) -> Dict[str, Any]:
        """
        对单张图片进行性能基准测试

        Args:
            image_path: 图片文件路径

        Returns:
            图片文件的性能统计数据
        """
        filename = os.path.basename(image_path)
        logger.info(f"📊 基准测试图片: {filename}")

        file_stats = {
            "filename": filename,
            "file_path": image_path,
            "file_type": "image",
            "total_time": 0,
            "image_count": 1,
            "image_stats": []
        }

        file_start_time = time.time()

        # 测试单张图片
        image_stat = self._benchmark_single_image_ocr(image_path, filename)
        file_stats["image_stats"].append(image_stat)

        file_stats["total_time"] = time.time() - file_start_time

        return file_stats

    def _benchmark_single_pdf(self, pdf_path: str, cleanup_temp: bool = True) -> Dict[str, Any]:
        """
        对单个PDF文件进行性能基准测试

        Args:
            pdf_path: PDF文件路径
            cleanup_temp: 是否清理临时文件

        Returns:
            PDF文件的性能统计数据
        """
        filename = os.path.basename(pdf_path)
        logger.info(f"📊 基准测试PDF: {filename}")

        file_stats = {
            "filename": filename,
            "file_path": pdf_path,
            "file_type": "pdf",
            "total_time": 0,
            "image_count": 0,
            "image_stats": [],
            "temp_dir": None
        }

        file_start_time = time.time()

        try:
            # PDF转换为图片
            convert_start = time.time()
            image_paths = self.pdf_to_images(pdf_path)
            convert_time = time.time() - convert_start

            if image_paths:
                file_stats["temp_dir"] = os.path.dirname(image_paths[0])

            logger.info(f"PDF转换耗时: {convert_time:.2f}秒，生成{len(image_paths)}张图片")

            # 对每张图片进行OCR基准测试
            for i, image_path in enumerate(image_paths, 1):
                page_name = f"{filename}_page_{i}"
                image_stat = self._benchmark_single_image_ocr(image_path, page_name)
                image_stat["pdf_convert_time"] = convert_time / len(image_paths)  # 分摊转换时间
                file_stats["image_stats"].append(image_stat)

            file_stats["image_count"] = len(image_paths)

        except Exception as e:
            logger.error(f"PDF基准测试失败: {e}")

        file_stats["total_time"] = time.time() - file_start_time

        return file_stats

    def _benchmark_directory(self, directory_path: str, cleanup_temp: bool = True) -> Dict[str, Any]:
        """
        对目录进行性能基准测试

        Args:
            directory_path: 目录路径
            cleanup_temp: 是否清理临时文件

        Returns:
            目录的性能统计数据
        """
        logger.info(f"📊 基准测试目录: {directory_path}")

        dir_stats = {
            "total_files": 0,
            "total_images": 0,
            "file_stats": [],
            "image_stats": [],
            "temp_dirs": []
        }

        # 获取所有支持的文件
        all_files = []
        for filename in os.listdir(directory_path):
            file_path = os.path.join(directory_path, filename)
            if os.path.isfile(file_path):
                is_supported, file_type = self.is_supported_file(filename)
                if is_supported:
                    all_files.append((file_path, filename, file_type))

        all_files.sort(key=lambda x: x[1])  # 按文件名排序

        # 处理每个文件
        for file_path, filename, file_type in all_files:
            try:
                if file_type == 'image':
                    file_stats = self._benchmark_single_image(file_path)
                elif file_type == 'pdf':
                    file_stats = self._benchmark_single_pdf(file_path, cleanup_temp)
                    if file_stats.get("temp_dir"):
                        dir_stats["temp_dirs"].append(file_stats["temp_dir"])

                dir_stats["file_stats"].append(file_stats)
                dir_stats["image_stats"].extend(file_stats["image_stats"])
                dir_stats["total_files"] += 1
                dir_stats["total_images"] += file_stats["image_count"]

            except Exception as e:
                logger.error(f"处理文件{filename}的基准测试失败: {e}")
                continue

        return dir_stats

    def _benchmark_single_image_ocr(self, image_data: Union[str, bytes], image_name: str) -> Dict[str, Any]:
        """
        对单张图片进行详细的OCR性能测试

        Args:
            image_data: 图片文件路径或字节数据
            image_name: 图片名称

        Returns:
            详细的性能统计数据
        """
        image_stat = {
            "image_name": image_name,
            "encode_time": 0,
            "api_time": 0,
            "process_time": 0,
            "total_time": 0,
            "char_count": 0,
            "success": False,
            "error": None
        }

        try:
            # 1. 计时开始
            start_time = time.time()

            # 2. Base64编码时间
            encode_start = time.time()
            encoded_str = self.get_base64(image_data)
            encode_time = time.time() - encode_start
            image_stat["encode_time"] = encode_time

            # 3. API请求时间（使用统一OCR服务）
            api_start = time.time()
            if isinstance(image_data, str):
                # 如果传入的是文件路径
                with open(image_data, "rb") as image_file:
                    image_bytes = image_file.read()
            elif isinstance(image_data, bytes):
                # 如果传入的是字节流
                image_bytes = image_data
            else:
                raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")
            
            # 使用统一OCR服务进行基准测试
            from common.clients.ocr_service import process_ocr
            result = process_ocr(image_bytes, use_correction=True)  # 启用角度矫正进行测试
            api_time = time.time() - api_start
            image_stat["api_time"] = api_time

            # 4. 结果处理时间
            process_start = time.time()
            # 直接使用统一OCR服务返回的markdown_text
            markdown_text = result.get("markdown_text", "")
            process_time = time.time() - process_start
            image_stat["process_time"] = process_time

            # 5. 总时间
            total_time = time.time() - start_time
            image_stat["total_time"] = total_time

            # 6. 识别结果统计
            char_count = len(markdown_text)
            image_stat["char_count"] = char_count
            image_stat["success"] = True

            logger.debug(f"📊 {image_name} - 编码:{encode_time:.2f}s, API:{api_time:.2f}s, "
                        f"处理:{process_time:.2f}s, 总计:{total_time:.2f}s, 字符:{char_count}")

        except Exception as e:
            image_stat["error"] = str(e)
            image_stat["total_time"] = time.time() - start_time
            logger.error(f"图片{image_name}基准测试失败: {e}")

        return image_stat

    def _generate_performance_summary(self, stats: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成性能总结报告

        Args:
            stats: 性能统计数据

        Returns:
            性能总结数据
        """
        summary = {
            "total_processing_time": stats["total_time"],
            "successful_images": 0,
            "failed_images": 0,
            "total_characters": 0,
            "avg_encode_time": 0,
            "avg_api_time": 0,
            "avg_process_time": 0,
            "min_time": float('inf'),
            "max_time": 0,
            "throughput_images_per_second": 0,
            "throughput_chars_per_second": 0
        }

        if not stats["per_image_stats"]:
            return summary

        # 统计成功和失败的图片
        successful_stats = [img for img in stats["per_image_stats"] if img["success"]]
        failed_stats = [img for img in stats["per_image_stats"] if not img["success"]]

        summary["successful_images"] = len(successful_stats)
        summary["failed_images"] = len(failed_stats)

        if successful_stats:
            # 计算平均时间
            summary["avg_encode_time"] = sum(img["encode_time"] for img in successful_stats) / len(successful_stats)
            summary["avg_api_time"] = sum(img["api_time"] for img in successful_stats) / len(successful_stats)
            summary["avg_process_time"] = sum(img["process_time"] for img in successful_stats) / len(successful_stats)

            # 计算最小和最大时间
            times = [img["total_time"] for img in successful_stats]
            summary["min_time"] = min(times)
            summary["max_time"] = max(times)

            # 计算总字符数
            summary["total_characters"] = sum(img["char_count"] for img in successful_stats)

            # 计算吞吐量
            if stats["total_time"] > 0:
                summary["throughput_images_per_second"] = summary["successful_images"] / stats["total_time"]
                summary["throughput_chars_per_second"] = summary["total_characters"] / stats["total_time"]

        return summary

    def _print_performance_report(self, stats: Dict[str, Any]):
        """
        打印性能测试报告

        Args:
            stats: 性能统计数据
        """
        print("\n" + "="*80)
        print("🚀 OCR性能基准测试报告")
        print("="*80)

        print(f"\n📁 输入信息:")
        print(f"   路径: {stats['input_path']}")
        print(f"   类型: {stats['input_type']}")
        print(f"   格式: {stats['file_format']}")

        print(f"\n📊 处理统计:")
        print(f"   总文件数: {stats['total_files']}")
        print(f"   总图片数: {stats['total_images']}")
        print(f"   总耗时: {stats['total_time']:.2f}秒")
        print(f"   平均每文件耗时: {stats['avg_time_per_file']:.2f}秒")
        print(f"   平均每图片耗时: {stats['avg_time_per_image']:.2f}秒")

        summary = stats["summary"]
        print(f"\n⚡ 性能指标:")
        print(f"   成功处理: {summary['successful_images']}/{stats['total_images']} 张图片")
        if summary["failed_images"] > 0:
            print(f"   失败处理: {summary['failed_images']} 张图片")
        print(f"   识别字符总数: {summary['total_characters']}")
        print(f"   图片处理吞吐量: {summary['throughput_images_per_second']:.2f} 张/秒")
        print(f"   字符识别吞吐量: {summary['throughput_chars_per_second']:.1f} 字符/秒")

        if summary["successful_images"] > 0:
            print(f"\n⏱️ 时间分析:")
            print(f"   平均编码时间: {summary['avg_encode_time']:.3f}秒")
            print(f"   平均API时间: {summary['avg_api_time']:.3f}秒")
            print(f"   平均处理时间: {summary['avg_process_time']:.3f}秒")
            print(f"   最快处理时间: {summary['min_time']:.3f}秒")
            print(f"   最慢处理时间: {summary['max_time']:.3f}秒")

        # 显示前5个最慢的图片
        if len(stats["per_image_stats"]) > 1:
            sorted_images = sorted(stats["per_image_stats"],
                                 key=lambda x: x["total_time"], reverse=True)
            print(f"\n🐌 处理时间最长的图片 (前5个):")
            for i, img in enumerate(sorted_images[:5], 1):
                status = "✅" if img["success"] else "❌"
                print(f"   {i}. {status} {img['image_name']}: {img['total_time']:.2f}秒 "
                      f"({img['char_count']} 字符)")

        print("\n" + "="*80)


# 便捷函数
def create_ocr_client(**kwargs) -> OCRClient:
    """
    创建OCR客户端实例的便捷函数

    Args:
        **kwargs: OCRClient构造函数的参数

    Returns:
        OCRClient实例
    """
    return OCRClient(**kwargs)


def ocr_process(input_path: str, cleanup_temp: bool = True, **kwargs) -> str:
    """
    智能OCR处理便捷函数（推荐使用）
    自动检测输入类型并处理文件或目录

    Args:
        input_path: 输入路径（文件或目录）
        cleanup_temp: 是否清理临时文件，默认True
        **kwargs: OCRClient构造函数的参数

    Returns:
        OCR识别结果文本
    """
    client = create_ocr_client(**kwargs)
    return client.process_input(input_path, cleanup_temp)


def ocr_image(image_data: Union[str, bytes], **kwargs) -> str:
    """
    对单张图片进行OCR识别的便捷函数

    Args:
        image_data: 图片文件路径或字节数据
        **kwargs: OCRClient构造函数的参数

    Returns:
        识别到的文本内容
    """
    client = create_ocr_client(**kwargs)
    return client.ocr_main(image_data)


def ocr_pdf(pdf_path: str, cleanup_temp: bool = True, **kwargs) -> str:
    """
    对PDF文件进行OCR识别的便捷函数

    Args:
        pdf_path: PDF文件路径
        cleanup_temp: 是否清理临时文件，默认True
        **kwargs: OCRClient构造函数的参数

    Returns:
        OCR识别结果文本
    """
    client = create_ocr_client(**kwargs)
    return client.process_pdf_file(pdf_path, cleanup_temp)


def ocr_directory(directory_path: str, cleanup_temp: bool = True, **kwargs) -> str:
    """
    对文件夹中所有支持文件进行OCR识别的便捷函数

    Args:
        directory_path: 文件夹路径
        cleanup_temp: 是否清理临时文件，默认True
        **kwargs: OCRClient构造函数的参数

    Returns:
        合并后的OCR文本结果
    """
    client = create_ocr_client(**kwargs)
    return client.process_directory(directory_path, cleanup_temp)


def ocr_benchmark(input_path: str, cleanup_temp: bool = True, **kwargs) -> Dict[str, Any]:
    """
    OCR性能基准测试便捷函数

    Args:
        input_path: 输入路径（文件或目录）
        cleanup_temp: 是否清理临时文件，默认True
        **kwargs: OCRClient构造函数的参数

    Returns:
        性能统计数据字典
    """
    client = create_ocr_client(**kwargs)
    return client.benchmark_ocr_performance(input_path, cleanup_temp)


if __name__ == "__main__":
    # 测试代码
    client = OCRClient()

    print("🚀 OCR客户端工具类已就绪！")
    print("\n📋 支持的功能:")
    print("1. 智能处理 - 自动检测输入类型: client.process_input(path)")
    print("2. 单张图片OCR: client.ocr_main(image_path)")
    print("3. PDF文件OCR: client.process_pdf_file(pdf_path)")
    print("4. 目录批量OCR: client.process_directory(directory_path)")
    print("5. 性能基准测试: client.benchmark_ocr_performance(path)")
    print("\n📁 支持的格式:")
    print(f"   图片格式: {', '.join(client.SUPPORTED_IMAGE_FORMATS)}")
    print(f"   PDF格式: {', '.join(client.SUPPORTED_PDF_FORMATS)}")
    print(f"   PDF支持状态: {'✅ 可用' if PDF_SUPPORT else '❌ 不可用'}")
    print(f"   PIL支持状态: {'✅ 可用' if PIL_SUPPORT else '❌ 不可用'}")

    # 使用示例
    print("\n💡 使用示例:")
    print("# 智能处理（推荐）")
    print("result = ocr_process('path/to/file_or_directory')")
    print("\n# 单张图片")
    print("result = ocr_image('path/to/image.jpg')")
    print("\n# PDF文件")
    print("result = ocr_pdf('path/to/document.pdf')")
    print("\n# 目录批量处理")
    print("result = ocr_directory('path/to/directory')")
    print("\n# 性能基准测试")
    print("stats = ocr_benchmark('path/to/file_or_directory')")
    print("# 或使用客户端方法")
    print("stats = client.benchmark_ocr_performance('path/to/input')")

    # 测试智能处理
    # test_path = r"D:\data\脱敏用\10张"
    # result = client.process_input(test_path)
    # print(f"智能处理结果: {result}")

    # 测试单张图片OCR
    # test_image_path = "path/to/test/image.jpg"
    # result = client.ocr_main(test_image_path)
    # print(f"图片OCR结果: {result}")

    # 测试PDF文件OCR
    # test_pdf_path = r"D:\下载\个人信息测试文档 -不含申办方.pdf"
    # result = client.process_pdf_file(test_pdf_path)
    # print(f"PDF OCR结果: {result}")

    # 测试目录批量OCR
    test_directory = r"D:\data\脱敏用\13tu"
    # test_directory = r"D:\data\脱敏用\36张"
    result = client.process_directory(test_directory)
    print(f"目录批量OCR结果: {result}")

    # 测试性能基准测试
    # test_benchmark_path = r"D:\data\脱敏用\10张"
    # stats = client.benchmark_ocr_performance(test_benchmark_path)
    # print(f"性能测试结果: {stats['summary']}")