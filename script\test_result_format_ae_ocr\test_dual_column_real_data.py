# -*- coding: utf-8 -*-
"""
真实数据双栏表格处理测试脚本

该脚本使用提供的真实检验单数据测试双栏表格布局的处理能力，
验证调整后的处理流程和增强版坐标提取功能。

测试数据来源：
- 文本数据：test_data/text_test_data.txt
- OCR数据：test_data/ocr_test_data.json

使用方法:
    python test_dual_column_real_data.py
"""

import json
import re
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from difflib import SequenceMatcher
from collections import defaultdict
from pathlib import Path


@dataclass
class TestItem:
    """测试项目数据模型"""
    test_code: str = ""
    test_name: str = ""
    test_value: str = ""
    test_unit: str = ""
    reference_value: str = ""
    abnormal_symbol: str = ""
    location: Optional[List[List[float]]] = None


@dataclass
class ItemCoordinate:
    """单个检验项目的坐标信息"""
    item_index: int
    test_code: str
    test_name: str
    location_points: List[List[float]]
    row_index: int
    column_index: int
    anchor_block_index: int
    matched_blocks: List[int]
    confidence: float


class RealDataProcessor:
    """真实数据处理器"""
    
    def __init__(self):
        """初始化处理器"""
        self.test_data_dir = Path(__file__).parent / "test_data"
    
    def load_real_data(self):
        """加载真实测试数据"""
        print("📂 加载真实测试数据...")
        
        # 加载文本数据
        text_file = self.test_data_dir / "text_test_data.txt"
        if text_file.exists():
            with open(text_file, 'r', encoding='utf-8') as f:
                self.text_data = f.read()
            print(f"  ✅ 加载文本数据: {len(self.text_data)} 字符")
        else:
            print(f"  ❌ 文本数据文件不存在: {text_file}")
            return False
        
        # 加载OCR数据
        ocr_file = self.test_data_dir / "ocr_test_data.json"
        if ocr_file.exists():
            with open(ocr_file, 'r', encoding='utf-8') as f:
                ocr_raw = json.load(f)
            
            # 转换为标准格式
            if isinstance(ocr_raw, dict) and "words_block_list" in ocr_raw:
                self.ocr_data = [ocr_raw]
            else:
                self.ocr_data = ocr_raw
            
            total_blocks = sum(len(page.get("words_block_list", [])) for page in self.ocr_data)
            print(f"  ✅ 加载OCR数据: {len(self.ocr_data)} 页, {total_blocks} 个文本块")
        else:
            print(f"  ❌ OCR数据文件不存在: {ocr_file}")
            return False
        
        return True
    
    def parse_text_data(self):
        """解析文本数据，提取检验项目"""
        print("\n🔍 解析文本数据，提取检验项目...")
        
        lines = self.text_data.split('\n')
        test_items = []
        
        # 查找检验项目数据行
        data_started = False
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否是表头行
            if "检验项目" in line and "结果" in line and "参考区间" in line:
                data_started = True
                print(f"  📋 找到表头行: {line}")
                continue
            
            if not data_started:
                continue
            
            # 跳过非数据行
            if any(keyword in line for keyword in ["检测仪器", "申请时间", "采样时间", "检验者", "审核者"]):
                break
            
            # 解析数据行
            items = self.parse_data_line(line)
            test_items.extend(items)
        
        print(f"  ✅ 解析完成，提取到 {len(test_items)} 个检验项目")
        
        # 显示前几个项目
        print("  📋 前5个检验项目:")
        for i, item in enumerate(test_items[:5]):
            print(f"    {i+1}. {item.test_code} - {item.test_name}: {item.test_value} {item.test_unit or ''}")
        
        self.test_items = test_items
        return test_items
    
    def parse_data_line(self, line: str) -> List[TestItem]:
        """解析单行数据，可能包含多个检验项目"""
        items = []
        
        # 使用正则表达式匹配检验项目模式
        # 模式：项目代码 项目名称 结果 单位 参考区间
        pattern = r'([A-Z][A-Z0-9]*\.?)\s+([^0-9\+\-阴性]+?)\s+([0-9\.\+\-阴性未见]+[↑↓]?)\s*([/a-zA-Z%]*)\s*([0-9\.\-阴性未见个/a-zA-Z%\$ε\s]*)'
        
        matches = re.findall(pattern, line)
        
        for match in matches:
            test_code, test_name, test_value, test_unit, reference_value = match
            
            # 清理数据
            test_code = test_code.strip()
            test_name = test_name.strip().replace('*', '').replace('（', '(').replace('）', ')')
            test_value = test_value.strip()
            test_unit = test_unit.strip()
            reference_value = reference_value.strip()
            
            # 检查异常符号
            abnormal_symbol = ""
            if "↑" in test_value:
                abnormal_symbol = "↑"
                test_value = test_value.replace("↑", "").strip()
            elif "↓" in test_value:
                abnormal_symbol = "↓"
                test_value = test_value.replace("↓", "").strip()
            
            # 创建测试项目
            if test_code and test_name and test_value:
                item = TestItem(
                    test_code=test_code,
                    test_name=test_name,
                    test_value=test_value,
                    test_unit=test_unit,
                    reference_value=reference_value,
                    abnormal_symbol=abnormal_symbol
                )
                items.append(item)
        
        return items
    
    def analyze_dual_column_layout(self):
        """分析双栏表格布局"""
        print("\n📊 分析双栏表格布局...")
        
        # 分析文本数据中的双栏模式
        lines = self.text_data.split('\n')
        dual_column_lines = []
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # 计算行中检验项目的数量
            pattern = r'([A-Z][A-Z0-9]*\.?)\s+([^0-9\+\-阴性]+?)\s+([0-9\.\+\-阴性未见]+[↑↓]?)'
            matches = re.findall(pattern, line)
            
            if len(matches) >= 2:
                dual_column_lines.append((line_num + 1, line, len(matches)))
        
        print(f"  📋 检测到 {len(dual_column_lines)} 行双栏数据:")
        for line_num, line, item_count in dual_column_lines:
            print(f"    行 {line_num}: {item_count} 个项目")
            print(f"      内容: {line[:80]}{'...' if len(line) > 80 else ''}")
        
        return dual_column_lines
    
    def extract_coordinates_from_ocr(self):
        """从OCR数据中提取坐标信息"""
        print("\n🎯 从OCR数据中提取坐标信息...")
        
        if not hasattr(self, 'test_items') or not self.test_items:
            print("  ⚠️ 请先解析文本数据")
            return []
        
        # 展开OCR块
        all_blocks = []
        for page_data in self.ocr_data:
            all_blocks.extend(page_data.get("words_block_list", []))
        
        print(f"  📊 OCR块总数: {len(all_blocks)}")
        
        # 简化的坐标提取逻辑
        item_coordinates = []
        
        for item_index, test_item in enumerate(self.test_items):
            # 查找匹配的OCR块
            best_match = None
            best_similarity = 0.0
            
            for block_index, block in enumerate(all_blocks):
                block_text = block.get('words', '')
                
                # 尝试匹配test_code
                if test_item.test_code:
                    similarity = self.calculate_similarity(test_item.test_code, block_text)
                    if similarity > best_similarity and similarity >= 0.8:
                        best_match = (block_index, similarity, 'test_code')
                        best_similarity = similarity
                
                # 尝试匹配test_name
                if test_item.test_name:
                    similarity = self.calculate_similarity(test_item.test_name, block_text)
                    if similarity > best_similarity and similarity >= 0.8:
                        best_match = (block_index, similarity, 'test_name')
                        best_similarity = similarity
            
            if best_match:
                block_index, similarity, match_type = best_match
                block = all_blocks[block_index]
                location = block.get('location', [])
                
                if location and len(location) == 4:
                    # 创建坐标信息
                    coord = ItemCoordinate(
                        item_index=item_index,
                        test_code=test_item.test_code,
                        test_name=test_item.test_name,
                        location_points=location,
                        row_index=-1,  # 稍后计算
                        column_index=-1,  # 稍后计算
                        anchor_block_index=block_index,
                        matched_blocks=[block_index],
                        confidence=similarity
                    )
                    item_coordinates.append(coord)
                    
                    print(f"    ✅ 项目 {item_index} ({test_item.test_name}) 匹配到块 {block_index} (相似度: {similarity:.2f})")
        
        print(f"  📍 成功提取 {len(item_coordinates)} 个项目的坐标")
        
        # 分析行列分布
        if item_coordinates:
            self.analyze_coordinate_distribution(item_coordinates)
        
        return item_coordinates
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        if not text1 or not text2:
            return 0.0
        
        # 清理文本
        clean_text1 = re.sub(r'\s+', '', text1.lower())
        clean_text2 = re.sub(r'\s+', '', text2.lower())
        
        return SequenceMatcher(None, clean_text1, clean_text2).ratio()
    
    def analyze_coordinate_distribution(self, item_coordinates: List[ItemCoordinate]):
        """分析坐标分布"""
        print("\n📊 分析坐标分布...")
        
        # 按Y坐标分组（行）
        y_groups = defaultdict(list)
        for coord in item_coordinates:
            # 计算中心Y坐标
            y_coords = [point[1] for point in coord.location_points]
            center_y = sum(y_coords) / len(y_coords)
            
            # 找到最接近的Y组
            best_group = None
            min_distance = float('inf')
            
            for group_y in y_groups.keys():
                distance = abs(center_y - group_y)
                if distance < min_distance and distance <= 20:  # 20像素容差
                    min_distance = distance
                    best_group = group_y
            
            if best_group is not None:
                y_groups[best_group].append(coord)
            else:
                y_groups[center_y] = [coord]
        
        # 按X坐标排序每行的项目
        sorted_rows = []
        for row_y in sorted(y_groups.keys()):
            row_coords = y_groups[row_y]
            # 按X坐标排序
            row_coords.sort(key=lambda c: sum(point[0] for point in c.location_points) / len(c.location_points))
            sorted_rows.append((row_y, row_coords))
        
        # 更新行列索引
        for row_index, (row_y, row_coords) in enumerate(sorted_rows):
            for col_index, coord in enumerate(row_coords):
                coord.row_index = row_index
                coord.column_index = col_index
        
        # 显示分布结果
        print(f"  📋 识别到 {len(sorted_rows)} 行:")
        for row_index, (row_y, row_coords) in enumerate(sorted_rows):
            print(f"    行 {row_index} (Y≈{row_y:.0f}): {len(row_coords)} 个项目")
            for coord in row_coords:
                x_coords = [point[0] for point in coord.location_points]
                center_x = sum(x_coords) / len(x_coords)
                print(f"      列 {coord.column_index}: {coord.test_name} (X≈{center_x:.0f})")
        
        # 检查双栏布局
        multi_column_rows = [(row_y, row_coords) for row_y, row_coords in sorted_rows if len(row_coords) > 1]
        if multi_column_rows:
            print(f"  🎯 检测到 {len(multi_column_rows)} 行双栏布局")
        else:
            print("  📝 未检测到明显的双栏布局")


def main():
    """主函数"""
    print("🚀 真实数据双栏表格处理测试")
    print("=" * 80)
    
    # 创建处理器
    processor = RealDataProcessor()
    
    # 加载数据
    if not processor.load_real_data():
        print("❌ 数据加载失败")
        return False
    
    # 解析文本数据
    test_items = processor.parse_text_data()
    if not test_items:
        print("❌ 文本数据解析失败")
        return False
    
    # 分析双栏布局
    dual_column_lines = processor.analyze_dual_column_layout()
    
    # 提取坐标信息
    item_coordinates = processor.extract_coordinates_from_ocr()
    
    # 生成总结报告
    print("\n📊 测试总结")
    print("=" * 80)
    print(f"📈 数据统计:")
    print(f"  解析项目数: {len(test_items)}")
    print(f"  双栏数据行: {len(dual_column_lines)}")
    print(f"  坐标匹配数: {len(item_coordinates)}")
    print(f"  坐标覆盖率: {len(item_coordinates)/len(test_items)*100:.1f}%")
    
    if item_coordinates:
        # 分析行列分布
        row_count = len(set(coord.row_index for coord in item_coordinates))
        max_columns = max(coord.column_index + 1 for coord in item_coordinates)
        print(f"  识别行数: {row_count}")
        print(f"  最大列数: {max_columns}")
        
        if max_columns > 1:
            print("  🎯 成功识别双栏表格布局")
        else:
            print("  📝 识别为单栏表格布局")
    
    print("\n🎉 真实数据双栏表格处理测试完成！")
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
