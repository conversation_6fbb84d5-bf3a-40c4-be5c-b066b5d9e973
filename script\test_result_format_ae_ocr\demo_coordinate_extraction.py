# -*- coding: utf-8 -*-
"""
坐标提取功能演示脚本

该脚本演示如何使用新的坐标提取功能来获取医疗检验报告中每行的坐标信息。

使用方法:
    python demo_coordinate_extraction.py <image_path>
    python demo_coordinate_extraction.py --demo  # 使用内置测试数据
"""

import sys
import os
import json
import argparse
from pathlib import Path

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from script.test_result_format_ae_ocr.test_main import process_single_image_with_coordinates
from script.test_result_format_ae_ocr.utils.ocr_client import OCRClient
from script.test_result_format_ae_ocr.utils.coordinate_extraction import (
    print_coordinate_summary, 
    export_coordinates_to_json,
    get_row_y_coordinates,
    get_row_bounding_boxes
)


def demo_with_builtin_data():
    """使用内置测试数据演示坐标提取功能"""
    print("🚀 坐标提取功能演示（使用内置测试数据）")
    print("=" * 60)
    
    # 使用main.py中的内置测试数据
    from script.test_result_format_ae_ocr.main import process_medical_ocr
    
    test_ocr_text = """[SMO-2022-6673-镇江市第三人民医院-000821-生命体征表.pdf]
[Page 1]
H206_葡萄糖（GLU) 华中科技大学同济医学院附属同济医院检验科报告单
姓 名：卢翔 类科病 别：临床试验 年龄：39岁 性 别男 样本编号：20240507G0028343
病人ID:2417 室： 送检医生：侯子君 条码号：********** 备注：
标本：血 区： 床号： 临床诊断：
检验项目 结果 提示参考区间 单位
葡萄糖 4.90 3.90-6.10 7/10m
号
XJ
2024.5.7
采样时间：2024-05-0712:16 接收时间：2024-05-07 检验者： 审核者：辉军
审核时间：2024-05-0713:41
联系地址：湖北省武汉市解放大道1095号 联系电话：027-83662916 注：本报告仅对送检标本负责！"""
    
    print("📝 处理内置测试数据...")
    test_items = process_medical_ocr(test_ocr_text)
    
    if test_items:
        print(f"✅ 成功识别 {len(test_items)} 个检验项目")
        for i, item in enumerate(test_items):
            print(f"  项目 {i}: {item.test_name} = {item.test_value}")
    else:
        print("❌ 未识别到检验项目")
    
    print("\n⚠️  注意：内置数据缺少OCR坐标信息，无法演示坐标提取功能")
    print("请使用实际图片文件进行完整演示：")
    print("python demo_coordinate_extraction.py <image_path>")


def demo_with_image(image_path: str):
    """使用实际图片演示坐标提取功能"""
    print(f"🚀 坐标提取功能演示（图片: {image_path}）")
    print("=" * 60)
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    try:
        # 创建OCR客户端
        ocr_client = OCRClient()
        
        # 处理图片（包含坐标提取）
        print("📝 正在处理图片...")
        result = process_single_image_with_coordinates(image_path, ocr_client)
        
        ocr_text = result["ocr_text"]
        test_items = result["test_items"]
        row_coordinates = result["row_coordinates"]
        ocr_blocks = result["ocr_blocks"]
        
        print(f"\n✅ 处理完成！")
        print(f"📄 OCR文本长度: {len(ocr_text)}")
        print(f"🔬 识别的检验项目数: {len(test_items)}")
        print(f"📍 提取的行坐标数: {len(row_coordinates)}")
        print(f"📦 OCR文本块数: {len(ocr_blocks)}")
        
        # 显示检验项目
        if test_items:
            print(f"\n🔬 识别的检验项目:")
            print("-" * 40)
            for i, item in enumerate(test_items):
                coord_info = ""
                if item.row_coordinates:
                    x_min, y_min, x_max, y_max = item.row_coordinates
                    coord_info = f" [坐标: ({x_min:.0f},{y_min:.0f})-({x_max:.0f},{y_max:.0f})]"
                print(f"  {i}: {item.test_name} = {item.test_value}{coord_info}")
        
        # 显示行坐标摘要
        if row_coordinates:
            print_coordinate_summary(row_coordinates, test_items)
            
            # 显示Y坐标列表
            y_coordinates = get_row_y_coordinates(row_coordinates)
            print(f"\n📏 行Y坐标范围:")
            for i, (y_min, y_max) in enumerate(y_coordinates):
                print(f"  行 {i}: Y {y_min:.1f} - {y_max:.1f}")
            
            # 显示边界框
            bounding_boxes = get_row_bounding_boxes(row_coordinates)
            print(f"\n📦 行边界框:")
            for i, (x_min, y_min, x_max, y_max) in enumerate(bounding_boxes):
                print(f"  行 {i}: ({x_min:.1f}, {y_min:.1f}) - ({x_max:.1f}, {y_max:.1f})")
        
        # 导出坐标信息到JSON
        if row_coordinates:
            output_file = f"{Path(image_path).stem}_coordinates.json"
            coordinate_data = export_coordinates_to_json(row_coordinates, test_items)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(coordinate_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 坐标信息已导出到: {output_file}")
        
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="医疗检验报告坐标提取功能演示")
    parser.add_argument("input", nargs="?", help="输入图片路径")
    parser.add_argument("--demo", action="store_true", help="使用内置测试数据演示")
    
    args = parser.parse_args()
    
    if args.demo:
        demo_with_builtin_data()
    elif args.input:
        demo_with_image(args.input)
    else:
        print("请提供图片路径或使用 --demo 参数")
        print("使用方法:")
        print("  python demo_coordinate_extraction.py <image_path>")
        print("  python demo_coordinate_extraction.py --demo")


if __name__ == "__main__":
    main()
