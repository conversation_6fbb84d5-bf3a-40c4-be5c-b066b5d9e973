# -*- coding: utf-8 -*-
"""
CoordinateExtractionNode节点专项测试脚本

该脚本专门测试CoordinateExtractionNode节点的功能，使用真实的大模型输出数据和OCR坐标数据。

测试内容：
1. 解析大模型输出的结构化数据
2. 加载真实的OCR坐标数据
3. 测试CoordinateExtractionNode的坐标提取功能
4. 验证输出格式与期望结果的一致性
5. 分析双栏表格布局的处理效果

使用方法:
    python test_coordinate_extraction_node.py
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入必要的模块
try:
    from script.test_result_format_ae_ocr.models import TestItem
    from script.test_result_format_ae_ocr.nodes import CoordinateExtractionNode
    from script.test_result_format_ae_ocr.utils.coordinate_extraction import extract_row_coordinates
    print("✅ 成功导入所有必要模块")
    DJANGO_AVAILABLE = True
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保已激活正确的conda环境: conda activate smo-ai-backend")
    print("🔄 使用独立实现继续测试...")
    DJANGO_AVAILABLE = False


# 独立的TestItem实现（当Django不可用时）
if not DJANGO_AVAILABLE:
    from dataclasses import dataclass

    @dataclass
    class TestItem:
        """独立的TestItem实现"""
        test_code: str = ""
        test_name: str = ""
        test_value: str = ""
        test_unit: str = ""
        reference_value: str = ""
        abnormal_symbol: str = ""
        page_num: Optional[int] = None
        location: Optional[List[List[float]]] = None


class CoordinateExtractionNodeTester:
    """CoordinateExtractionNode节点测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_data_dir = Path(__file__).parent / "test_data"
        self.llm_output_data = None
        self.ocr_data = None
        self.test_items = []
        
    def load_llm_output_data(self):
        """加载大模型输出数据"""
        print("📂 解析大模型输出数据...")
        
        # 大模型输出的JSON数据
        llm_output_json = '''[
{"tc":"SG","tn":"*(干化学)比重","tv":"6101","as":"","tu":"1.003-1.030","rf":""},
{"tc":"SPERM","tn":"（尿流式)精子","tv":"0","as":"","tu":"/ul","rf":"0-0"},
{"tc":"GLU","tn":"*(干化学)葡萄糖","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"SRC","tn":"(尿流式)小圆上皮细胞","tv":"0.4","as":"","tu":"n/$","rf":"ε-0"},
{"tc":"PRO","tn":"*(干化学)蛋白","tv":"+","as":"↑","tu":"","rf":"阴性"},
{"tc":"EC","tn":"(尿流式)上皮细胞","tv":"0","as":"↑","tu":"/uL","rf":"0-5"},
{"tc":"PH","tn":"*(干化学)酸度","tv":"7.5","as":"","tu":"4.5-8.0","rf":""},
{"tc":"KET","tn":"*(干化学)酮体","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"UBG","tn":"*(干化学)尿胆原","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"BLD","tn":"*(干化学)潜血","tv":"++","as":"1","tu":"阴性","rf":""},
{"tc":"LEU","tn":"*（干化学）白细胞酯酶","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"NIT","tn":"*(干化学)亚硝酸盐","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"BII.","tn":"*(干化学)胆红素","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"WBC","tn":"(尿流式)白细胞","tv":"27","as":"↑","tu":"/ul","rf":"0-11"},
{"tc":"RBC","tn":"(尿流式)红细胞","tv":"6139","as":"1","tu":"/↓","rf":"0-14"},
{"tc":"","tn":"（尿流式)完整红细胞百分比","tv":"100.0","as":"","tu":"%","rf":""},
{"tc":"BACT","tn":"(尿流式)细菌","tv":"13","as":"","tu":"/uL","rf":"0-51"},
{"tc":"YLC","tn":"(尿流式)酵母样菌","tv":"0","as":"","tu":"/ul.","rf":"0"},
{"tc":"X,TAI.","tn":"（尿流式）结品数量","tv":"0","as":"","tu":"/ul","rf":"0"},
{"tc":"X,TAI.1","tn":"（镜检)结晶类别","tv":"/","as":"","tu":"","rf":""},
{"tc":"MUS","tn":"(尿流式)粘液丝","tv":"0.00","as":"","tu":"/ul","rf":""},
{"tc":"CAST1","tn":"（镜检）管型类别1","tv":"未见","as":"","tu":"个/LPF","rf":""},
{"tc":"CAST2","tn":"(镜检)管型类别2","tv":"","as":"","tu":"个/LPF","rf":""}
]'''
        
        try:
            self.llm_output_data = json.loads(llm_output_json)
            print(f"  ✅ 解析大模型输出: {len(self.llm_output_data)} 个检验项目")
            
            # 显示前几个项目
            print("  📋 前5个检验项目:")
            for i, item in enumerate(self.llm_output_data[:5]):
                print(f"    {i+1}. {item['tc']} - {item['tn']}: {item['tv']}")
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"  ❌ JSON解析失败: {e}")
            return False
    
    def load_ocr_data(self):
        """加载OCR坐标数据"""
        print("\n📂 加载OCR坐标数据...")
        
        ocr_file = self.test_data_dir / "ocr_test_data.json"
        if not ocr_file.exists():
            print(f"  ❌ OCR数据文件不存在: {ocr_file}")
            return False
        
        try:
            with open(ocr_file, 'r', encoding='utf-8') as f:
                ocr_raw = json.load(f)
            
            # 转换为标准格式
            if isinstance(ocr_raw, dict) and "words_block_list" in ocr_raw:
                self.ocr_data = [ocr_raw]  # 包装成列表格式
            else:
                self.ocr_data = ocr_raw
            
            total_blocks = sum(len(page.get("words_block_list", [])) for page in self.ocr_data)
            print(f"  ✅ 加载OCR数据: {len(self.ocr_data)} 页, {total_blocks} 个文本块")
            
            # 显示前几个OCR块
            if self.ocr_data and self.ocr_data[0].get("words_block_list"):
                print("  📋 前5个OCR文本块:")
                for i, block in enumerate(self.ocr_data[0]["words_block_list"][:5]):
                    words = block.get("words", "")
                    confidence = block.get("confidence", 0)
                    print(f"    {i+1}. '{words}' (置信度: {confidence:.2f})")
            
            return True
            
        except Exception as e:
            print(f"  ❌ OCR数据加载失败: {e}")
            return False
    
    def convert_llm_output_to_test_items(self):
        """将大模型输出转换为TestItem对象"""
        print("\n🔄 转换大模型输出为TestItem对象...")
        
        if not self.llm_output_data:
            print("  ❌ 大模型输出数据为空")
            return False
        
        try:
            self.test_items = []
            
            for i, item_data in enumerate(self.llm_output_data):
                # 创建TestItem对象
                test_item = TestItem(
                    test_code=item_data.get("tc", ""),
                    test_name=item_data.get("tn", ""),
                    test_value=item_data.get("tv", ""),
                    test_unit=item_data.get("tu", ""),
                    reference_value=item_data.get("rf", ""),
                    abnormal_symbol=item_data.get("as", ""),
                    page_num=1
                )
                self.test_items.append(test_item)
            
            print(f"  ✅ 转换完成: {len(self.test_items)} 个TestItem对象")
            
            # 显示转换结果
            print("  📋 转换后的TestItem示例:")
            for i, item in enumerate(self.test_items[:3]):
                print(f"    {i+1}. {item.test_code} - {item.test_name}: {item.test_value}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 转换失败: {e}")
            return False
    
    def test_coordinate_extraction_node(self):
        """测试CoordinateExtractionNode节点"""
        print("\n🧪 测试CoordinateExtractionNode节点...")
        print("-" * 60)

        if not self.test_items or not self.ocr_data:
            print("  ❌ 测试数据不完整")
            return {'success': False, 'error': '测试数据不完整'}

        try:
            if DJANGO_AVAILABLE:
                # 使用真实的CoordinateExtractionNode
                coord_node = CoordinateExtractionNode()

                # 准备输入数据
                input_data = {
                    "processed_items": self.test_items,
                    "ocr_blocks": self.ocr_data
                }

                print(f"  📊 输入数据:")
                print(f"    测试项目数: {len(self.test_items)}")
                print(f"    OCR页数: {len(self.ocr_data)}")

                # 执行坐标提取
                start_time = time.time()
                prep_result = coord_node.prep({"processed_items": self.test_items, "ocr_blocks": self.ocr_data})
                row_coordinates = coord_node.exec(prep_result)
                processing_time = time.time() - start_time

                print(f"  ✅ 坐标提取完成，用时: {processing_time:.2f}秒")
                print(f"  📍 提取到 {len(row_coordinates)} 行坐标")

                # 检查TestItem对象是否被更新了坐标信息
                items_with_location = [item for item in self.test_items if hasattr(item, 'location') and item.location]

            else:
                # 使用独立实现进行坐标提取
                print("  🔄 使用独立实现进行坐标提取...")

                start_time = time.time()
                items_with_location = self.extract_coordinates_standalone()
                processing_time = time.time() - start_time

                print(f"  ✅ 独立坐标提取完成，用时: {processing_time:.2f}秒")
                row_coordinates = []  # 独立实现不返回行坐标对象

            print(f"  📍 包含坐标信息的项目: {len(items_with_location)} 个")

            # 显示坐标信息示例
            if items_with_location:
                print("\n  📋 坐标信息示例:")
                for i, item in enumerate(items_with_location[:3]):
                    if item.location:
                        x_min, y_min = item.location[0]  # 左上
                        x_max, y_max = item.location[2]  # 右下
                        print(f"    {i+1}. {item.test_name}: ({x_min:.1f},{y_min:.1f}) - ({x_max:.1f},{y_max:.1f})")

            return {
                'success': True,
                'processing_time': processing_time,
                'row_coordinates': row_coordinates,
                'items_with_location': items_with_location
            }

        except Exception as e:
            print(f"  ❌ 坐标提取测试失败: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': str(e)}

    def extract_coordinates_standalone(self):
        """独立的坐标提取实现"""
        from difflib import SequenceMatcher
        import re

        # 展开OCR块
        all_blocks = []
        for page_data in self.ocr_data:
            all_blocks.extend(page_data.get("words_block_list", []))

        items_with_location = []

        for item in self.test_items:
            best_match = None
            best_similarity = 0.0

            # 查找最佳匹配的OCR块
            for block_index, block in enumerate(all_blocks):
                block_text = block.get('words', '')

                # 尝试匹配test_name
                if item.test_name:
                    clean_item_name = re.sub(r'\s+', '', item.test_name.lower())
                    clean_block_text = re.sub(r'\s+', '', block_text.lower())
                    similarity = SequenceMatcher(None, clean_item_name, clean_block_text).ratio()

                    if similarity > best_similarity and similarity >= 0.7:
                        best_match = (block_index, similarity, block)
                        best_similarity = similarity

            # 如果找到匹配，设置坐标
            if best_match:
                block_index, similarity, block = best_match
                location = block.get('location', [])

                if location and len(location) == 4:
                    item.location = location
                    items_with_location.append(item)
                    print(f"    ✅ 项目 {item.test_name} 匹配到块 {block_index} (相似度: {similarity:.2f})")

        return items_with_location
    
    def generate_expected_output_format(self, test_result):
        """生成期望的输出格式"""
        print("\n📋 生成期望的输出格式...")
        print("-" * 60)
        
        if not test_result['success'] or not test_result['items_with_location']:
            print("  ⚠️ 无坐标信息，无法生成输出格式")
            return
        
        # 生成words_block_list格式的输出
        words_block_list = []
        
        for item in test_result['items_with_location']:
            if item.location:
                # 计算置信度（模拟）
                confidence = 0.96  # 默认置信度
                
                block_entry = {
                    "words": item.test_name,
                    "confidence": confidence,
                    "location": item.location
                }
                words_block_list.append(block_entry)
        
        # 输出JSON格式
        output_data = {
            "words_block_list": words_block_list
        }
        
        print("  📊 生成的输出格式:")
        print(json.dumps(output_data, ensure_ascii=False, indent=2))
        
        # 验证格式
        print(f"\n  ✅ 输出验证:")
        print(f"    总项目数: {len(words_block_list)}")
        
        for i, block in enumerate(words_block_list[:3]):
            print(f"    项目 {i+1}: {block['words']}")
            print(f"      置信度: {block['confidence']}")
            print(f"      坐标点数: {len(block['location'])}")
            
            # 验证坐标格式
            if len(block['location']) == 4:
                x_coords = [point[0] for point in block['location']]
                y_coords = [point[1] for point in block['location']]
                print(f"      X范围: {min(x_coords):.1f} - {max(x_coords):.1f}")
                print(f"      Y范围: {min(y_coords):.1f} - {max(y_coords):.1f}")
        
        return output_data
    
    def analyze_dual_column_layout(self, test_result):
        """分析双栏表格布局"""
        print("\n📊 分析双栏表格布局...")
        print("-" * 60)
        
        if not test_result['success'] or not test_result['items_with_location']:
            print("  ⚠️ 无坐标信息，无法分析布局")
            return
        
        items_with_coords = test_result['items_with_location']
        
        # 按Y坐标分组（行）
        y_groups = {}
        for item in items_with_coords:
            if item.location:
                # 计算中心Y坐标
                y_coords = [point[1] for point in item.location]
                center_y = sum(y_coords) / len(y_coords)
                
                # 找到最接近的Y组
                best_group = None
                min_distance = float('inf')
                
                for group_y in y_groups.keys():
                    distance = abs(center_y - group_y)
                    if distance < min_distance and distance <= 30:  # 30像素容差
                        min_distance = distance
                        best_group = group_y
                
                if best_group is not None:
                    y_groups[best_group].append(item)
                else:
                    y_groups[center_y] = [item]
        
        # 分析每行的列分布
        print(f"  📋 识别到 {len(y_groups)} 行:")
        
        dual_column_rows = 0
        for row_y in sorted(y_groups.keys()):
            row_items = y_groups[row_y]
            
            # 按X坐标排序
            row_items.sort(key=lambda item: sum(point[0] for point in item.location) / len(item.location))
            
            print(f"    行 Y≈{row_y:.0f}: {len(row_items)} 个项目")
            
            if len(row_items) > 1:
                dual_column_rows += 1
                print(f"      🎯 双栏布局行")
                
                for col_idx, item in enumerate(row_items):
                    x_coords = [point[0] for point in item.location]
                    center_x = sum(x_coords) / len(x_coords)
                    print(f"        列 {col_idx}: {item.test_name} (X≈{center_x:.0f})")
            else:
                item = row_items[0]
                print(f"      📝 单栏: {item.test_name}")
        
        print(f"\n  🎯 双栏布局统计:")
        print(f"    总行数: {len(y_groups)}")
        print(f"    双栏行数: {dual_column_rows}")
        print(f"    单栏行数: {len(y_groups) - dual_column_rows}")
        print(f"    双栏比例: {dual_column_rows/len(y_groups)*100:.1f}%")
        
        return {
            'total_rows': len(y_groups),
            'dual_column_rows': dual_column_rows,
            'single_column_rows': len(y_groups) - dual_column_rows
        }
    
    def validate_output_format(self, output_data):
        """验证输出格式的正确性"""
        print("\n✅ 验证输出格式...")
        print("-" * 60)
        
        if not output_data or "words_block_list" not in output_data:
            print("  ❌ 输出格式不正确")
            return False
        
        words_block_list = output_data["words_block_list"]
        
        print(f"  📊 格式验证:")
        print(f"    words_block_list项目数: {len(words_block_list)}")
        
        # 验证每个项目的格式
        valid_items = 0
        for i, block in enumerate(words_block_list):
            try:
                # 检查必需字段
                assert "words" in block, f"项目 {i} 缺少words字段"
                assert "confidence" in block, f"项目 {i} 缺少confidence字段"
                assert "location" in block, f"项目 {i} 缺少location字段"
                
                # 检查location格式
                location = block["location"]
                assert len(location) == 4, f"项目 {i} location应有4个坐标点"
                
                for j, point in enumerate(location):
                    assert len(point) == 2, f"项目 {i} 坐标点 {j} 应有2个值"
                    assert isinstance(point[0], (int, float)), f"项目 {i} 坐标点 {j} X坐标类型错误"
                    assert isinstance(point[1], (int, float)), f"项目 {i} 坐标点 {j} Y坐标类型错误"
                
                valid_items += 1
                
            except AssertionError as e:
                print(f"    ❌ 项目 {i} 格式错误: {e}")
        
        print(f"    ✅ 格式正确的项目: {valid_items}/{len(words_block_list)}")
        print(f"    📊 格式正确率: {valid_items/len(words_block_list)*100:.1f}%")
        
        return valid_items == len(words_block_list)


def main():
    """主函数"""
    print("🚀 CoordinateExtractionNode节点专项测试")
    print("=" * 80)
    
    # 创建测试器
    tester = CoordinateExtractionNodeTester()
    
    # 加载测试数据
    print("📂 第一步：加载测试数据")
    if not tester.load_llm_output_data():
        print("❌ 大模型输出数据加载失败")
        return False
    
    if not tester.load_ocr_data():
        print("❌ OCR数据加载失败")
        return False
    
    # 转换数据格式
    print("\n🔄 第二步：数据格式转换")
    if not tester.convert_llm_output_to_test_items():
        print("❌ 数据格式转换失败")
        return False
    
    # 测试CoordinateExtractionNode
    print("\n🧪 第三步：测试CoordinateExtractionNode")
    test_result = tester.test_coordinate_extraction_node()
    
    if not test_result['success']:
        print("❌ CoordinateExtractionNode测试失败")
        return False
    
    # 生成期望的输出格式
    print("\n📋 第四步：生成输出格式")
    output_data = tester.generate_expected_output_format(test_result)
    
    # 分析双栏表格布局
    print("\n📊 第五步：分析双栏表格布局")
    layout_analysis = tester.analyze_dual_column_layout(test_result)
    
    # 验证输出格式
    print("\n✅ 第六步：验证输出格式")
    format_valid = tester.validate_output_format(output_data)
    
    # 生成最终报告
    print("\n📊 测试总结报告")
    print("=" * 80)
    
    print(f"📈 测试统计:")
    print(f"  大模型输出项目数: {len(tester.llm_output_data)}")
    print(f"  转换TestItem数: {len(tester.test_items)}")
    print(f"  提取行坐标数: {len(test_result.get('row_coordinates', []))}")
    print(f"  包含坐标项目数: {len(test_result.get('items_with_location', []))}")
    
    if layout_analysis:
        print(f"\n📊 布局分析:")
        print(f"  总行数: {layout_analysis['total_rows']}")
        print(f"  双栏行数: {layout_analysis['dual_column_rows']}")
        print(f"  单栏行数: {layout_analysis['single_column_rows']}")
        print(f"  双栏比例: {layout_analysis['dual_column_rows']/layout_analysis['total_rows']*100:.1f}%")
    
    print(f"\n✅ 功能验证:")
    print(f"  CoordinateExtractionNode: {'✅ 正常' if test_result['success'] else '❌ 异常'}")
    print(f"  输出格式: {'✅ 正确' if format_valid else '❌ 错误'}")
    print(f"  双栏布局支持: {'✅ 支持' if layout_analysis and layout_analysis['dual_column_rows'] > 0 else '📝 单栏'}")
    
    if test_result['success'] and format_valid:
        print("\n🎉 CoordinateExtractionNode节点测试完全通过！")
        print("✅ 节点功能正常，输出格式正确，支持双栏表格布局")
        return True
    else:
        print("\n⚠️ 测试中发现问题，请检查相关功能")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
