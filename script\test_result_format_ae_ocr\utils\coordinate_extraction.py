"""
坐标提取工具：从结构化检验报告中提取行坐标信息

该模块提供了将结构化的检验项目数据映射回原始OCR坐标的功能，
支持提取整行的坐标信息，用于后续的定位和标注需求。

主要功能：
1. 文本匹配：将结构化项目文本与OCR文本块进行匹配
2. 行坐标提取：从匹配的文本块中提取行级别的坐标信息
3. 边界框计算：计算每行的完整边界框（bounding box）

使用方法：
    from script.test_result_format_ae_ocr.utils.coordinate_extraction import extract_row_coordinates
    
    # 提取行坐标
    row_coordinates = extract_row_coordinates(test_items, ocr_text_blocks)
"""

import re
import time
import logging
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from difflib import SequenceMatcher

logger = logging.getLogger(__name__)


@dataclass
class RowCoordinate:
    """行坐标数据模型"""
    row_index: int                          # 行索引（从0开始）
    y_min: float                           # 行的最小Y坐标
    y_max: float                           # 行的最大Y坐标
    x_min: float                           # 行的最小X坐标
    x_max: float                           # 行的最大X坐标
    center_y: float                        # 行的中心Y坐标
    height: float                          # 行高度
    width: float                           # 行宽度
    matched_blocks: List[Dict[str, Any]]   # 匹配到的OCR文本块
    confidence: float                      # 匹配置信度
    test_item_indices: List[int]           # 关联的测试项目索引


class CoordinateMapper:
    """坐标映射器：将结构化数据映射回OCR坐标"""
    
    def __init__(self, similarity_threshold: float = 0.6, y_tolerance: float = 10.0):
        """
        初始化坐标映射器
        
        Args:
            similarity_threshold: 文本相似度阈值，用于匹配判断
            y_tolerance: Y坐标容差，用于判断是否在同一行
        """
        self.similarity_threshold = similarity_threshold
        self.y_tolerance = y_tolerance
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            
        Returns:
            相似度分数 (0.0-1.0)
        """
        if not text1 or not text2:
            return 0.0
        
        # 清理文本：移除空格和特殊字符
        clean_text1 = re.sub(r'\s+', '', text1.lower())
        clean_text2 = re.sub(r'\s+', '', text2.lower())
        
        # 使用序列匹配器计算相似度
        return SequenceMatcher(None, clean_text1, clean_text2).ratio()
    
    def find_matching_blocks(self, target_text: str, ocr_blocks: List[Dict[str, Any]]) -> List[Tuple[int, float]]:
        """
        查找与目标文本匹配的OCR文本块
        
        Args:
            target_text: 目标文本（来自结构化数据）
            ocr_blocks: OCR文本块列表
            
        Returns:
            匹配的块索引和相似度分数列表
        """
        matches = []
        
        for i, block in enumerate(ocr_blocks):
            block_text = block.get('words', '')
            similarity = self.calculate_text_similarity(target_text, block_text)
            
            if similarity >= self.similarity_threshold:
                matches.append((i, similarity))
        
        # 按相似度降序排序
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches
    
    def map_item_to_blocks(self, test_item, ocr_blocks: List[Dict[str, Any]]) -> List[int]:
        """
        将单个测试项目映射到OCR文本块
        
        Args:
            test_item: 测试项目对象（TestItem）
            ocr_blocks: OCR文本块列表
            
        Returns:
            匹配的OCR块索引列表
        """
        matched_block_indices = []
        
        # 构建搜索文本：组合测试项目的关键字段
        search_texts = []
        
        # 添加测试代码
        if hasattr(test_item, 'test_code') and test_item.test_code:
            search_texts.append(test_item.test_code)
        
        # 添加测试名称
        if hasattr(test_item, 'test_name') and test_item.test_name:
            search_texts.append(test_item.test_name)
        
        # 添加测试值
        if hasattr(test_item, 'test_value') and test_item.test_value:
            search_texts.append(test_item.test_value)
        
        # 添加单位
        if hasattr(test_item, 'test_unit') and test_item.test_unit:
            search_texts.append(test_item.test_unit)
        
        # 添加参考范围到搜索文本
        if hasattr(test_item, 'reference_value') and test_item.reference_value:
            search_texts.append(test_item.reference_value)

        # 为每个搜索文本查找匹配的块
        for search_text in search_texts:
            matches = self.find_matching_blocks(search_text, ocr_blocks)
            for block_idx, similarity in matches:
                if block_idx not in matched_block_indices:
                    matched_block_indices.append(block_idx)
                    logger.debug(f"匹配文本 '{search_text}' 到块 {block_idx} (相似度: {similarity:.2f})")
        
        return matched_block_indices


class RowCoordinateExtractor:
    """行坐标提取器：从OCR坐标中提取行级别的坐标信息"""

    def __init__(self):
        """
        初始化行坐标提取器

        使用line_break标记来确定行边界，不再依赖Y坐标容差
        """
        pass
    
    def get_block_center_y(self, location: List[List[float]]) -> float:
        """
        计算文本块的中心Y坐标
        
        Args:
            location: 文本块的四角坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
            
        Returns:
            中心Y坐标
        """
        if not location or len(location) != 4:
            return 0.0
        
        y_coords = [point[1] for point in location]
        return sum(y_coords) / len(y_coords)
    
    def get_block_bounds(self, location: List[List[float]]) -> Tuple[float, float, float, float]:
        """
        计算文本块的边界坐标
        
        Args:
            location: 文本块的四角坐标
            
        Returns:
            (x_min, y_min, x_max, y_max)
        """
        if not location or len(location) != 4:
            return 0.0, 0.0, 0.0, 0.0
        
        x_coords = [point[0] for point in location]
        y_coords = [point[1] for point in location]
        
        return min(x_coords), min(y_coords), max(x_coords), max(y_coords)
    
    def group_blocks_by_line_break(self, block_indices: List[int], ocr_blocks: List[Dict[str, Any]]) -> List[List[int]]:
        """
        根据line_break标记将文本块分组为行

        Args:
            block_indices: 要分组的文本块索引列表
            ocr_blocks: OCR文本块列表

        Returns:
            按行分组的文本块索引列表
        """
        if not block_indices:
            return []

        # 按索引排序，保持原始顺序
        sorted_indices = sorted(block_indices)

        rows = []
        current_row = []

        for idx in sorted_indices:
            if idx < len(ocr_blocks):
                block = ocr_blocks[idx]
                current_row.append(idx)

                # 检查是否有line_break标记
                if block.get('line_break', False):
                    # 遇到line_break，结束当前行
                    if current_row:
                        rows.append(current_row)
                        current_row = []

        # 添加最后一行（如果有剩余的块）
        if current_row:
            rows.append(current_row)

        return rows
    
    def calculate_row_coordinates(self, row_block_indices: List[int], 
                                ocr_blocks: List[Dict[str, Any]], 
                                row_index: int,
                                test_item_indices: List[int] = None) -> RowCoordinate:
        """
        计算行的坐标信息
        
        Args:
            row_block_indices: 该行包含的OCR块索引列表
            ocr_blocks: OCR文本块列表
            row_index: 行索引
            test_item_indices: 关联的测试项目索引列表
            
        Returns:
            行坐标对象
        """
        if not row_block_indices:
            return RowCoordinate(
                row_index=row_index,
                y_min=0.0, y_max=0.0, x_min=0.0, x_max=0.0,
                center_y=0.0, height=0.0, width=0.0,
                matched_blocks=[], confidence=0.0,
                test_item_indices=test_item_indices or []
            )
        
        # 收集所有块的坐标和信息
        all_x_coords = []
        all_y_coords = []
        matched_blocks = []
        total_confidence = 0.0
        
        for block_idx in row_block_indices:
            if block_idx < len(ocr_blocks):
                block = ocr_blocks[block_idx]
                location = block.get('location', [])
                confidence = block.get('confidence', 1.0)
                
                if location and len(location) == 4:
                    # 提取所有坐标点
                    for point in location:
                        all_x_coords.append(point[0])
                        all_y_coords.append(point[1])
                    
                    matched_blocks.append(block)
                    total_confidence += confidence
        
        if not all_x_coords or not all_y_coords:
            return RowCoordinate(
                row_index=row_index,
                y_min=0.0, y_max=0.0, x_min=0.0, x_max=0.0,
                center_y=0.0, height=0.0, width=0.0,
                matched_blocks=[], confidence=0.0,
                test_item_indices=test_item_indices or []
            )
        
        # 计算边界坐标
        x_min, x_max = min(all_x_coords), max(all_x_coords)
        y_min, y_max = min(all_y_coords), max(all_y_coords)
        center_y = (y_min + y_max) / 2
        height = y_max - y_min
        width = x_max - x_min

        # 计算平均置信度
        avg_confidence = total_confidence / len(matched_blocks) if matched_blocks else 0.0

        return RowCoordinate(
            row_index=row_index,
            y_min=y_min, y_max=y_max, x_min=x_min, x_max=x_max,
            center_y=center_y, height=height, width=width,
            matched_blocks=matched_blocks, confidence=avg_confidence,
            test_item_indices=test_item_indices or []
        )

    def calculate_row_location_points(self, row_coordinate: RowCoordinate) -> List[List[float]]:
        """
        根据行坐标计算四个坐标点

        Args:
            row_coordinate: 行坐标对象

        Returns:
            四个坐标点：[[左上x,左上y], [右上x,右上y], [右下x,右下y], [左下x,左下y]]
        """
        x_min, y_min = row_coordinate.x_min, row_coordinate.y_min
        x_max, y_max = row_coordinate.x_max, row_coordinate.y_max

        return [
            [x_min, y_min],  # 左上
            [x_max, y_min],  # 右上
            [x_max, y_max],  # 右下
            [x_min, y_max]   # 左下
        ]


def map_items_to_coordinates(test_items: List[Any], ocr_blocks: List[Dict[str, Any]], 
                           similarity_threshold: float = 0.6) -> Dict[int, List[int]]:
    """
    将测试项目映射到OCR文本块
    
    Args:
        test_items: 测试项目列表
        ocr_blocks: OCR文本块列表
        similarity_threshold: 文本相似度阈值
        
    Returns:
        测试项目索引到OCR块索引列表的映射
    """
    mapper = CoordinateMapper(similarity_threshold=similarity_threshold)
    item_to_blocks = {}
    
    for i, test_item in enumerate(test_items):
        matched_blocks = mapper.map_item_to_blocks(test_item, ocr_blocks)
        if matched_blocks:
            item_to_blocks[i] = matched_blocks
            logger.debug(f"测试项目 {i} 映射到 {len(matched_blocks)} 个OCR块")
        else:
            logger.warning(f"测试项目 {i} 未找到匹配的OCR块")
    
    return item_to_blocks


def extract_row_coordinates(test_items: List[Any], ocr_data: List[Dict[str, Any]],
                          similarity_threshold: float = 0.6) -> List[RowCoordinate]:
    """
    从结构化测试报告中提取行坐标信息

    Args:
        test_items: 结构化的测试项目列表
        ocr_data: OCR数据列表，格式为 [{"page": int, "words_block_list": [...]}]
        similarity_threshold: 文本匹配相似度阈值

    Returns:
        行坐标信息列表
    """
    logger.info(f"开始提取行坐标，测试项目数: {len(test_items)}, OCR页数: {len(ocr_data)}")

    if not test_items or not ocr_data:
        logger.warning("测试项目或OCR数据为空，无法提取坐标")
        return []

    # 展开所有页面的OCR文本块
    all_ocr_blocks = []
    for page_data in ocr_data:
        words_block_list = page_data.get("words_block_list", [])
        all_ocr_blocks.extend(words_block_list)

    logger.info(f"展开后OCR块总数: {len(all_ocr_blocks)}")
    
    # 第一步：将测试项目映射到OCR文本块
    item_to_blocks = map_items_to_coordinates(test_items, all_ocr_blocks, similarity_threshold)

    if not item_to_blocks:
        logger.warning("没有找到任何匹配的OCR块")
        return []

    # 第二步：收集所有匹配的块索引
    all_matched_blocks = set()
    for block_indices in item_to_blocks.values():
        all_matched_blocks.update(block_indices)

    # 第三步：使用line_break标记按行分组OCR块
    extractor = RowCoordinateExtractor()
    row_groups = extractor.group_blocks_by_line_break(list(all_matched_blocks), all_ocr_blocks)
    
    logger.info(f"识别到 {len(row_groups)} 行数据")
    
    # 第四步：为每行计算坐标信息
    row_coordinates = []
    
    for row_idx, row_block_indices in enumerate(row_groups):
        # 找到该行关联的测试项目
        associated_items = []
        for item_idx, block_indices in item_to_blocks.items():
            # 检查是否有交集
            if set(block_indices) & set(row_block_indices):
                associated_items.append(item_idx)
        
        # 计算行坐标
        row_coord = extractor.calculate_row_coordinates(
            row_block_indices, all_ocr_blocks, row_idx, associated_items
        )
        
        row_coordinates.append(row_coord)
        
        logger.debug(f"行 {row_idx}: Y坐标 {row_coord.y_min:.1f}-{row_coord.y_max:.1f}, "
                    f"关联项目 {associated_items}, 置信度 {row_coord.confidence:.2f}")
    
    # 按Y坐标排序（从上到下）
    row_coordinates.sort(key=lambda x: x.center_y)
    
    # 重新分配行索引
    for i, row_coord in enumerate(row_coordinates):
        row_coord.row_index = i
    
    logger.info(f"行坐标提取完成，共提取 {len(row_coordinates)} 行")
    return row_coordinates


def get_row_y_coordinates(row_coordinates: List[RowCoordinate]) -> List[Tuple[float, float]]:
    """
    获取所有行的Y坐标范围
    
    Args:
        row_coordinates: 行坐标列表
        
    Returns:
        (y_min, y_max) 元组列表
    """
    return [(row.y_min, row.y_max) for row in row_coordinates]


def get_row_bounding_boxes(row_coordinates: List[RowCoordinate]) -> List[Tuple[float, float, float, float]]:
    """
    获取所有行的完整边界框
    
    Args:
        row_coordinates: 行坐标列表
        
    Returns:
        (x_min, y_min, x_max, y_max) 元组列表
    """
    return [(row.x_min, row.y_min, row.x_max, row.y_max) for row in row_coordinates]


def filter_rows_by_test_items(row_coordinates: List[RowCoordinate],
                             target_item_indices: List[int]) -> List[RowCoordinate]:
    """
    根据测试项目索引过滤行坐标

    Args:
        row_coordinates: 行坐标列表
        target_item_indices: 目标测试项目索引列表

    Returns:
        包含目标测试项目的行坐标列表
    """
    filtered_rows = []

    for row in row_coordinates:
        # 检查该行是否包含目标测试项目
        if any(item_idx in row.test_item_indices for item_idx in target_item_indices):
            filtered_rows.append(row)

    return filtered_rows


def print_coordinate_summary(row_coordinates: List[RowCoordinate], test_items: List[Any] = None):
    """
    打印行坐标信息摘要

    Args:
        row_coordinates: 行坐标列表
        test_items: 测试项目列表（可选，用于显示关联信息）
    """
    if not row_coordinates:
        print("📍 未找到行坐标信息")
        return

    print(f"\n📍 行坐标提取结果（共 {len(row_coordinates)} 行）:")
    print("=" * 80)

    headers = ["行号", "Y坐标范围", "X坐标范围", "中心Y", "行高", "置信度", "关联项目"]
    table_data = []

    for row in row_coordinates:
        # 构建关联项目信息
        associated_items = []
        if test_items and row.test_item_indices:
            for item_idx in row.test_item_indices:
                if item_idx < len(test_items):
                    item = test_items[item_idx]
                    item_name = getattr(item, 'test_name', f'项目{item_idx}')
                    associated_items.append(f"{item_idx}:{item_name[:10]}")

        associated_str = ", ".join(associated_items) if associated_items else "无"

        table_data.append([
            row.row_index,
            f"{row.y_min:.1f}-{row.y_max:.1f}",
            f"{row.x_min:.1f}-{row.x_max:.1f}",
            f"{row.center_y:.1f}",
            f"{row.height:.1f}",
            f"{row.confidence:.2f}",
            associated_str
        ])

    from tabulate import tabulate
    print(tabulate(table_data, headers=headers, tablefmt="grid"))
    print("=" * 80)


def export_coordinates_to_json(row_coordinates: List[RowCoordinate],
                              test_items: List[Any] = None) -> Dict[str, Any]:
    """
    将行坐标信息导出为JSON格式

    Args:
        row_coordinates: 行坐标列表
        test_items: 测试项目列表（可选）

    Returns:
        包含坐标信息的字典
    """
    result = {
        "total_rows": len(row_coordinates),
        "extraction_timestamp": time.time(),
        "rows": []
    }

    for row in row_coordinates:
        row_data = {
            "row_index": row.row_index,
            "coordinates": {
                "x_min": row.x_min,
                "y_min": row.y_min,
                "x_max": row.x_max,
                "y_max": row.y_max,
                "center_y": row.center_y,
                "height": row.height,
                "width": row.width
            },
            "confidence": row.confidence,
            "matched_blocks_count": len(row.matched_blocks),
            "test_item_indices": row.test_item_indices,
            "associated_test_items": []
        }

        # 添加关联的测试项目信息
        if test_items and row.test_item_indices:
            for item_idx in row.test_item_indices:
                if item_idx < len(test_items):
                    item = test_items[item_idx]
                    item_info = {
                        "index": item_idx,
                        "test_code": getattr(item, 'test_code', ''),
                        "test_name": getattr(item, 'test_name', ''),
                        "test_value": getattr(item, 'test_value', '')
                    }
                    row_data["associated_test_items"].append(item_info)

        result["rows"].append(row_data)

    return result
