# -*- coding: utf-8 -*-
"""
双栏表格布局坐标提取独立测试脚本

该脚本包含完整的双栏表格坐标提取功能，避免复杂的依赖关系，
专门用于验证双栏表格布局的处理能力。
"""

import re
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from difflib import SequenceMatcher
from collections import defaultdict


@dataclass
class ItemCoordinate:
    """单个检验项目的坐标信息"""
    item_index: int                         # 项目索引
    test_code: str                         # 测试代码
    test_name: str                         # 测试名称
    location_points: List[List[float]]     # 四个坐标点
    row_index: int                         # 所在行索引
    column_index: int                      # 所在列索引（0=第一列，1=第二列）
    anchor_block_index: int                # 锚点块索引
    matched_blocks: List[int]              # 所有匹配的块索引
    confidence: float                      # 匹配置信度


@dataclass
class ColumnBoundary:
    """列边界信息"""
    column_index: int                      # 列索引
    x_start: float                        # 列开始X坐标
    x_end: float                          # 列结束X坐标
    x_center: float                       # 列中心X坐标
    width: float                          # 列宽度


class SimpleTestItem:
    """简化的测试项目类"""
    def __init__(self, test_code="", test_name="", test_value="", test_unit="", reference_value=""):
        self.test_code = test_code
        self.test_name = test_name
        self.test_value = test_value
        self.test_unit = test_unit
        self.reference_value = reference_value
        self.location = None


class EnhancedCoordinateMapper:
    """增强版坐标映射器"""
    
    def __init__(self, exact_match_threshold: float = 0.9, fuzzy_match_threshold: float = 0.7):
        self.exact_match_threshold = exact_match_threshold
        self.fuzzy_match_threshold = fuzzy_match_threshold
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0.0
        
        clean_text1 = re.sub(r'\s+', '', text1.lower())
        clean_text2 = re.sub(r'\s+', '', text2.lower())
        
        return SequenceMatcher(None, clean_text1, clean_text2).ratio()
    
    def find_anchor_block(self, test_item, ocr_blocks: List[Dict[str, Any]]) -> Optional[Tuple[int, float, str]]:
        """查找检验项目的锚点块（test_code或test_name的最佳匹配）"""
        best_match = None
        best_similarity = 0.0
        best_field = ""
        
        # 优先匹配test_code
        if hasattr(test_item, 'test_code') and test_item.test_code:
            for i, block in enumerate(ocr_blocks):
                block_text = block.get('words', '')
                similarity = self.calculate_text_similarity(test_item.test_code, block_text)
                
                if similarity >= self.exact_match_threshold and similarity > best_similarity:
                    best_match = i
                    best_similarity = similarity
                    best_field = "test_code"
        
        # 如果test_code匹配不够好，尝试test_name
        if best_similarity < self.exact_match_threshold and hasattr(test_item, 'test_name') and test_item.test_name:
            for i, block in enumerate(ocr_blocks):
                block_text = block.get('words', '')
                similarity = self.calculate_text_similarity(test_item.test_name, block_text)
                
                if similarity >= self.exact_match_threshold and similarity > best_similarity:
                    best_match = i
                    best_similarity = similarity
                    best_field = "test_name"
        
        if best_match is not None:
            return (best_match, best_similarity, best_field)
        
        return None
    
    def find_related_blocks_in_row(self, anchor_index: int, test_item, ocr_blocks: List[Dict[str, Any]], 
                                  row_blocks: List[int]) -> List[int]:
        """在同一行中查找与检验项目相关的其他块"""
        related_blocks = [anchor_index]
        
        # 获取锚点块的X坐标
        anchor_block = ocr_blocks[anchor_index]
        anchor_location = anchor_block.get('location', [])
        if not anchor_location or len(anchor_location) != 4:
            return related_blocks
        
        anchor_x = sum(point[0] for point in anchor_location) / 4
        
        # 在同一行中查找相关的块
        search_texts = []
        if hasattr(test_item, 'test_value') and test_item.test_value:
            search_texts.append(test_item.test_value)
        if hasattr(test_item, 'test_unit') and test_item.test_unit:
            search_texts.append(test_item.test_unit)
        if hasattr(test_item, 'reference_value') and test_item.reference_value:
            search_texts.append(test_item.reference_value)
        
        for block_idx in row_blocks:
            if block_idx == anchor_index or block_idx in related_blocks:
                continue
            
            block = ocr_blocks[block_idx]
            block_text = block.get('words', '')
            block_location = block.get('location', [])
            
            if not block_location or len(block_location) != 4:
                continue
            
            block_x = sum(point[0] for point in block_location) / 4
            
            # 检查是否在合理的X坐标范围内
            x_distance = abs(block_x - anchor_x)
            if x_distance > 300:  # 超过300像素认为是不同列的项目
                continue
            
            # 检查文本匹配
            for search_text in search_texts:
                similarity = self.calculate_text_similarity(search_text, block_text)
                if similarity >= self.fuzzy_match_threshold:
                    related_blocks.append(block_idx)
                    break
        
        return related_blocks


class ColumnBoundaryAnalyzer:
    """列边界分析器"""
    
    def __init__(self, min_column_width: float = 100.0):
        self.min_column_width = min_column_width
    
    def analyze_column_boundaries(self, ocr_blocks: List[Dict[str, Any]]) -> List[ColumnBoundary]:
        """分析OCR块的X坐标分布，识别列边界"""
        if not ocr_blocks:
            return []
        
        # 收集所有块的X坐标
        x_coordinates = []
        for block in ocr_blocks:
            location = block.get('location', [])
            if location and len(location) == 4:
                x_coords = [point[0] for point in location]
                x_min, x_max = min(x_coords), max(x_coords)
                x_center = (x_min + x_max) / 2
                x_coordinates.append((x_min, x_center, x_max))
        
        if not x_coordinates:
            return []
        
        # 分析X坐标分布，识别列边界
        x_centers = [x_center for x_min, x_center, x_max in x_coordinates]
        x_centers.sort()
        
        # 使用聚类方法识别列中心
        column_centers = []
        current_cluster = [x_centers[0]]
        
        for x in x_centers[1:]:
            if x - current_cluster[-1] <= 50:  # 50像素内认为是同一列
                current_cluster.append(x)
            else:
                # 新列
                cluster_center = sum(current_cluster) / len(current_cluster)
                column_centers.append(cluster_center)
                current_cluster = [x]
        
        # 添加最后一个聚类
        if current_cluster:
            cluster_center = sum(current_cluster) / len(current_cluster)
            column_centers.append(cluster_center)
        
        # 生成列边界
        boundaries = []
        overall_x_min = min(x_min for x_min, _, _ in x_coordinates)
        overall_x_max = max(x_max for _, _, x_max in x_coordinates)
        
        if len(column_centers) == 1:
            # 单列布局
            boundaries.append(ColumnBoundary(0, overall_x_min, overall_x_max, column_centers[0], overall_x_max - overall_x_min))
        else:
            # 多列布局
            for i, center in enumerate(column_centers):
                if i == 0:
                    x_start = overall_x_min
                    x_end = (center + column_centers[i + 1]) / 2 if i + 1 < len(column_centers) else overall_x_max
                elif i == len(column_centers) - 1:
                    x_start = (column_centers[i - 1] + center) / 2
                    x_end = overall_x_max
                else:
                    x_start = (column_centers[i - 1] + center) / 2
                    x_end = (center + column_centers[i + 1]) / 2
                
                width = x_end - x_start
                boundaries.append(ColumnBoundary(i, x_start, x_end, center, width))
        
        return boundaries
    
    def determine_block_column(self, block_location: List[List[float]], 
                              column_boundaries: List[ColumnBoundary]) -> int:
        """确定文本块属于哪一列"""
        if not block_location or len(block_location) != 4 or not column_boundaries:
            return 0
        
        # 计算块的中心X坐标
        x_coords = [point[0] for point in block_location]
        block_x_center = sum(x_coords) / len(x_coords)
        
        # 找到最接近的列
        best_column = 0
        min_distance = float('inf')
        
        for boundary in column_boundaries:
            distance = abs(block_x_center - boundary.x_center)
            if distance < min_distance:
                min_distance = distance
                best_column = boundary.column_index
        
        return best_column


def extract_dual_column_coordinates_standalone(test_items: List[Any], ocr_data: List[Dict[str, Any]]) -> List[ItemCoordinate]:
    """独立版本的双栏表格坐标提取函数"""
    if not test_items or not ocr_data:
        return []
    
    # 展开所有页面的OCR文本块
    all_ocr_blocks = []
    for page_data in ocr_data:
        words_block_list = page_data.get("words_block_list", [])
        all_ocr_blocks.extend(words_block_list)
    
    print(f"展开后OCR块总数: {len(all_ocr_blocks)}")
    
    # 创建分析器
    mapper = EnhancedCoordinateMapper()
    column_analyzer = ColumnBoundaryAnalyzer()
    
    # 第一步：分析列边界
    column_boundaries = column_analyzer.analyze_column_boundaries(all_ocr_blocks)
    print(f"识别到 {len(column_boundaries)} 列")
    for i, boundary in enumerate(column_boundaries):
        print(f"  列 {i}: X坐标 {boundary.x_start:.1f} - {boundary.x_end:.1f} (中心: {boundary.x_center:.1f})")
    
    # 第二步：提取所有行
    all_rows = []
    current_row = []
    
    for i, block in enumerate(all_ocr_blocks):
        current_row.append(i)
        
        if block.get('line_break', False):
            if current_row:
                all_rows.append(current_row)
                current_row = []
    
    if current_row:
        all_rows.append(current_row)
    
    print(f"识别到 {len(all_rows)} 行")
    
    # 第三步：为每个测试项目查找坐标
    item_coordinates = []
    
    for item_index, test_item in enumerate(test_items):
        best_coordinate = None
        best_row_index = -1
        
        # 在所有行中查找该项目的最佳匹配
        for row_index, row_blocks in enumerate(all_rows):
            # 查找锚点块
            anchor_result = mapper.find_anchor_block(test_item, all_ocr_blocks)
            if not anchor_result:
                continue
            
            anchor_index, anchor_similarity, anchor_field = anchor_result
            
            # 确保锚点块在当前行中
            if anchor_index not in row_blocks:
                continue
            
            # 确定锚点块所在的列
            anchor_block = all_ocr_blocks[anchor_index]
            anchor_location = anchor_block.get('location', [])
            column_index = column_analyzer.determine_block_column(anchor_location, column_boundaries)
            
            # 查找该项目在当前行中的相关块
            related_blocks = mapper.find_related_blocks_in_row(
                anchor_index, test_item, all_ocr_blocks, row_blocks
            )
            
            # 过滤：只保留同一列的块
            if column_boundaries:
                filtered_blocks = []
                for block_idx in related_blocks:
                    block = all_ocr_blocks[block_idx]
                    block_location = block.get('location', [])
                    block_column = column_analyzer.determine_block_column(block_location, column_boundaries)
                    if block_column == column_index:
                        filtered_blocks.append(block_idx)
                related_blocks = filtered_blocks
            
            if not related_blocks:
                related_blocks = [anchor_index]
            
            # 计算项目的坐标范围
            all_x_coords = []
            all_y_coords = []
            total_confidence = 0.0
            
            for block_idx in related_blocks:
                block = all_ocr_blocks[block_idx]
                location = block.get('location', [])
                confidence = block.get('confidence', 1.0)
                
                if location and len(location) == 4:
                    for point in location:
                        all_x_coords.append(point[0])
                        all_y_coords.append(point[1])
                    total_confidence += confidence
            
            if not all_x_coords or not all_y_coords:
                continue
            
            # 计算边界坐标
            x_min, x_max = min(all_x_coords), max(all_x_coords)
            y_min, y_max = min(all_y_coords), max(all_y_coords)
            
            # 生成四个坐标点
            location_points = [
                [x_min, y_min],  # 左上
                [x_max, y_min],  # 右上
                [x_max, y_max],  # 右下
                [x_min, y_max]   # 左下
            ]
            
            # 计算平均置信度
            avg_confidence = total_confidence / len(related_blocks) if related_blocks else 0.0
            
            coordinate = ItemCoordinate(
                item_index=item_index,
                test_code=getattr(test_item, 'test_code', ''),
                test_name=getattr(test_item, 'test_name', ''),
                location_points=location_points,
                row_index=row_index,
                column_index=column_index,
                anchor_block_index=anchor_index,
                matched_blocks=related_blocks,
                confidence=avg_confidence
            )
            
            if best_coordinate is None or coordinate.confidence > best_coordinate.confidence:
                best_coordinate = coordinate
                best_row_index = row_index
        
        if best_coordinate:
            item_coordinates.append(best_coordinate)
            print(f"项目 {item_index} ({best_coordinate.test_name}) 定位到行 {best_coordinate.row_index} 列 {best_coordinate.column_index}")
    
    # 按行和列排序
    item_coordinates.sort(key=lambda x: (x.row_index, x.column_index))
    
    print(f"双栏表格坐标提取完成，成功定位 {len(item_coordinates)} 个项目")
    return item_coordinates


def create_dual_column_test_data():
    """创建双栏表格测试数据"""
    # 模拟双栏表格的测试项目
    test_items = [
        SimpleTestItem(
            test_code="GLU",
            test_name="葡萄糖",
            test_value="4.90",
            test_unit="mmol/L",
            reference_value="3.90-6.10"
        ),
        SimpleTestItem(
            test_code="CHOL",
            test_name="胆固醇",
            test_value="5.2",
            test_unit="mmol/L",
            reference_value="3.0-5.0"
        ),
        SimpleTestItem(
            test_code="TG",
            test_name="甘油三酯",
            test_value="1.8",
            test_unit="mmol/L",
            reference_value="0.5-1.7"
        ),
        SimpleTestItem(
            test_code="HDL",
            test_name="高密度脂蛋白",
            test_value="1.2",
            test_unit="mmol/L",
            reference_value="1.0-2.0"
        )
    ]
    
    # 双栏表格的OCR数据：两行，每行两个检验项
    ocr_data = [
        {
            "page": 1,
            "words_block_list": [
                # 表头（跳过，不影响测试）
                
                # 第一行数据：葡萄糖 + 胆固醇
                {"words": "GLU", "location": [[50, 200], [80, 200], [80, 220], [50, 220]], "confidence": 0.95},
                {"words": "葡萄糖", "location": [[85, 200], [135, 200], [135, 220], [85, 220]], "confidence": 0.95},
                {"words": "4.90", "location": [[150, 200], [180, 200], [180, 220], [150, 220]], "confidence": 0.98},
                {"words": "3.90-6.10", "location": [[200, 200], [270, 200], [270, 220], [200, 220]], "confidence": 0.92},
                {"words": "mmol/L", "location": [[300, 200], [350, 200], [350, 220], [300, 220]], "confidence": 0.90},
                
                {"words": "CHOL", "location": [[400, 200], [440, 200], [440, 220], [400, 220]], "confidence": 0.93},
                {"words": "胆固醇", "location": [[445, 200], [495, 200], [495, 220], [445, 220]], "confidence": 0.93},
                {"words": "5.2", "location": [[500, 200], [520, 200], [520, 220], [500, 220]], "confidence": 0.97},
                {"words": "3.0-5.0", "location": [[550, 200], [600, 200], [600, 220], [550, 220]], "confidence": 0.91},
                {"words": "mmol/L", "location": [[650, 200], [700, 200], [700, 220], [650, 220]], "confidence": 0.90, "line_break": True},
                
                # 第二行数据：甘油三酯 + 高密度脂蛋白
                {"words": "TG", "location": [[50, 250], [70, 250], [70, 270], [50, 270]], "confidence": 0.94},
                {"words": "甘油三酯", "location": [[75, 250], [135, 250], [135, 270], [75, 270]], "confidence": 0.96},
                {"words": "1.8", "location": [[150, 250], [170, 250], [170, 270], [150, 270]], "confidence": 0.99},
                {"words": "0.5-1.7", "location": [[200, 250], [250, 250], [250, 270], [200, 270]], "confidence": 0.88},
                {"words": "mmol/L", "location": [[300, 250], [350, 250], [350, 270], [300, 270]], "confidence": 0.90},
                
                {"words": "HDL", "location": [[400, 250], [430, 250], [430, 270], [400, 270]], "confidence": 0.92},
                {"words": "高密度脂蛋白", "location": [[435, 250], [515, 250], [515, 270], [435, 270]], "confidence": 0.94},
                {"words": "1.2", "location": [[500, 250], [520, 250], [520, 270], [500, 270]], "confidence": 0.98},
                {"words": "1.0-2.0", "location": [[550, 250], [600, 250], [600, 270], [550, 270]], "confidence": 0.89},
                {"words": "mmol/L", "location": [[650, 250], [700, 250], [700, 270], [650, 270]], "confidence": 0.90, "line_break": True}
            ]
        }
    ]
    
    return test_items, ocr_data


def test_dual_column_functionality():
    """测试双栏表格功能"""
    print("🚀 双栏表格布局坐标提取测试")
    print("=" * 80)
    
    # 创建测试数据
    test_items, ocr_data = create_dual_column_test_data()
    
    print("📝 测试数据:")
    print(f"  测试项目数: {len(test_items)}")
    for i, item in enumerate(test_items):
        print(f"    {i}: {item.test_code} - {item.test_name} = {item.test_value}")
    
    print(f"  OCR页数: {len(ocr_data)}")
    total_blocks = sum(len(page['words_block_list']) for page in ocr_data)
    print(f"  总OCR块数: {total_blocks}")
    
    # 显示双栏布局结构
    print("\n📊 双栏表格布局:")
    print("  第一行: 葡萄糖 (左列) + 胆固醇 (右列)")
    print("  第二行: 甘油三酯 (左列) + 高密度脂蛋白 (右列)")
    
    # 执行坐标提取
    print("\n🔍 执行增强版坐标提取...")
    item_coordinates = extract_dual_column_coordinates_standalone(test_items, ocr_data)
    
    print(f"✅ 提取完成，识别到 {len(item_coordinates)} 个项目坐标")
    
    # 验证结果
    assert len(item_coordinates) == 4, f"应该提取到4个项目坐标，实际提取到{len(item_coordinates)}个"
    
    # 验证行和列分布
    row_distribution = {}
    column_distribution = {}
    
    for coord in item_coordinates:
        row_distribution[coord.row_index] = row_distribution.get(coord.row_index, 0) + 1
        column_distribution[coord.column_index] = column_distribution.get(coord.column_index, 0) + 1
    
    print(f"  行分布: {row_distribution}")
    print(f"  列分布: {column_distribution}")
    
    # 显示详细结果
    print("\n📍 双栏表格坐标提取结果:")
    print("=" * 100)
    
    # 按行分组显示
    rows = defaultdict(list)
    for coord in item_coordinates:
        rows[coord.row_index].append(coord)
    
    for row_index in sorted(rows.keys()):
        row_items = sorted(rows[row_index], key=lambda x: x.column_index)
        print(f"\n行 {row_index}:")
        
        for coord in row_items:
            x_min, y_min = coord.location_points[0]  # 左上
            x_max, y_max = coord.location_points[2]  # 右下
            print(f"  列 {coord.column_index}: {coord.test_name}")
            print(f"    坐标: ({x_min:.1f},{y_min:.1f}) - ({x_max:.1f},{y_max:.1f})")
            print(f"    置信度: {coord.confidence:.2f}")
            print(f"    锚点块: {coord.anchor_block_index}")
    
    print("=" * 100)
    
    # 验证双栏布局：应该有2行，每行2个项目
    assert len(row_distribution) == 2, f"应该有2行，实际有{len(row_distribution)}行"
    assert all(count == 2 for count in row_distribution.values()), "每行应该有2个项目"
    assert len(column_distribution) == 2, f"应该有2列，实际有{len(column_distribution)}列"
    assert all(count == 2 for count in column_distribution.values()), "每列应该有2个项目"
    
    print("\n✅ 所有验证通过！")
    print("🎯 双栏表格布局坐标提取测试完成！")


if __name__ == "__main__":
    test_dual_column_functionality()
