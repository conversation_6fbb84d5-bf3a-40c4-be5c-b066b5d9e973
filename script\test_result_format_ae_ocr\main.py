# -*- coding: utf-8 -*-
"""
医疗检验单识别系统核心模块

核心功能：
- 医疗检验单OCR文本处理和结构化提取

注意：测试、评估和批量处理功能请使用 test_main.py
"""

import sys
import os
import json
import logging
import re
from tabulate import tabulate
from typing import List, Tuple, Dict, Optional

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录 (假设项目根目录是 d:\Code\smo-ai-backend)
# 向上两级目录，从 script/test_result_format_ae_ocr/main.py 到 d:\Code\smo-ai-backend
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from script.test_result_format_ae_ocr.flow import create_flow
from script.test_result_format_ae_ocr.utils.coordinate_extraction import extract_row_coordinates

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def split_ocr_text_by_pages(ocr_text: str) -> List[Tuple[str, int]]:
    """
    将多页OCR文本按页面拆分
    
    Args:
        ocr_text: 可能包含多页的OCR文本
        
    Returns:
        List[Tuple[str, int]]: 每页内容和页码的列表
    """
    if not ocr_text or not ocr_text.strip():
        return []
    
    try:
        # 使用正则表达式匹配页面标记 [Page N]
        page_pattern = r'\[Page\s+(\d+)\]'
        
        # 查找所有页面标记的位置
        page_matches = list(re.finditer(page_pattern, ocr_text, re.IGNORECASE))
        
        # 防御性检查：如果没有找到页面标记或没有找到[Page 1]，按单页处理
        if not page_matches:
            logger.info("未找到页面标记，按单页处理")
            return [(ocr_text.strip(), 1)]
        
        # 检查是否存在[Page 1]，如果不存在则按单页处理
        has_page_1 = any(int(match.group(1)) == 1 for match in page_matches)
        if not has_page_1:
            logger.info("未找到[Page 1]标记，按单页处理")
            return [(ocr_text.strip(), 1)]
        
        pages = []
        
        for i, match in enumerate(page_matches):
            page_num = int(match.group(1))
            start_pos = match.end()  # 页面标记之后的位置
            
            # 确定当前页面的结束位置
            if i + 1 < len(page_matches):
                # 不是最后一页，结束位置是下一个页面标记的开始
                end_pos = page_matches[i + 1].start()
            else:
                # 最后一页，结束位置是文本末尾
                end_pos = len(ocr_text)
            
            # 提取页面内容
            page_content = ocr_text[start_pos:end_pos].strip()
            
            if page_content:  # 只添加非空页面
                pages.append((page_content, page_num))
                logger.info(f"提取到第{page_num}页，内容长度: {len(page_content)}")
        
        # 防御性检查：如果拆分后没有有效页面，按单页处理
        if not pages:
            logger.warning("页面拆分后没有有效内容，按单页处理")
            return [(ocr_text.strip(), 1)]
        
        logger.info(f"总共拆分出 {len(pages)} 页")
        return pages
        
    except Exception as e:
        # 防御性处理：如果拆分过程中出现任何异常，按单页处理
        logger.warning(f"页面拆分过程中发生异常: {e}，按单页处理")
        return [(ocr_text.strip(), 1)]


def process_single_page_ocr(page_content: str, page_num: int = 1, task_info: dict = None,
                           ocr_blocks: Optional[List[Dict]] = None):
    """
    处理单页医疗检验单OCR文本

    Args:
        page_content: 单页OCR文本内容
        page_num: 页码（用于日志记录）
        task_info: 任务信息字典，用于LLM调用日志记录
        ocr_blocks: 可选的OCR文本块列表（包含坐标信息），传入时启用坐标提取

    Returns:
        List[TestItem]: 处理后的检验项目列表（包含坐标信息，如果提供了ocr_blocks）
    """
    logger.info(f"开始处理第{page_num}页" + ("（包含坐标提取）" if ocr_blocks else ""))

    # 根据是否提供OCR块决定是否启用坐标提取
    enable_coordinates = ocr_blocks is not None and len(ocr_blocks) > 0
    flow = create_flow(enable_coordinate_extraction=enable_coordinates)

    # 🎯 准备共享数据，包含任务信息和OCR块
    shared = {
        "ocr_text": page_content,
        "task_info": task_info or {},  # 传递任务信息到共享存储
        "ocr_blocks": ocr_blocks or []  # 传递OCR块信息
    }
    
    try:
        # 运行流程
        flow.run(shared)
        
        # 获取结果
        result = shared.get("processed_items")
        if not result:
            logger.warning(f"第{page_num}页流程执行完成但未生成结果")
            return []
        
        # 获取各种统计信息
        llm_stats = shared.get("llm_stats", {})
        llm_time = llm_stats.get('processing_time', 0)
        llm_chars = llm_stats.get('response_length', 0)

        # 为每个结果设置页码
        for item in result:
            item.page_num = page_num
        
        # 输出页面处理统计信息
        row_coordinates = shared.get("row_coordinates", [])
        coord_info = f"，{len(row_coordinates)} 行坐标" if row_coordinates else ""
        logger.info(f"第{page_num}页处理完成，识别到 {len(result)} 个检验项目{coord_info}")
        if llm_stats:
            logger.info(f"第{page_num}页LLM处理: {llm_time:.2f}秒, 输出{llm_chars}字符")

        return result
        
    except Exception as e:
        logger.error(f"处理第{page_num}页时发生错误: {e}")
        return []


def process_medical_ocr(ocr_text: str, task_info: dict = None,
                       ocr_blocks: Optional[List[Dict]] = None):
    """
    处理医疗检验单OCR文本（支持多页）

    Args:
        ocr_text: OCR识别的文本，可能包含多页
        task_info: 任务信息字典，包含task_id, create_user等日志参数
        ocr_blocks: 可选的OCR文本块列表（包含坐标信息），传入时启用坐标提取

    Returns:
        List[TestItem]: 处理后的检验项目列表（包含坐标信息，如果提供了ocr_blocks）
    """
    logger.info("开始处理医疗检验单OCR文本")
    
    # 1. 拆分页面
    pages = split_ocr_text_by_pages(ocr_text)
    
    if not pages:
        logger.warning("没有有效的页面内容")
        return []
    
    # 2. 处理每一页并收集结果
    all_results = []
    total_items = 0
    
    for page_content, page_num in pages:
        try:
            # 🎯 传递任务信息和OCR块到单页处理函数
            page_results = process_single_page_ocr(page_content, page_num, task_info, ocr_blocks)
            if page_results:
                all_results.extend(page_results)
                total_items += len(page_results)
                logger.info(f"第{page_num}页成功处理，获得 {len(page_results)} 个检验项目")
            else:
                logger.warning(f"第{page_num}页未获得有效结果")
        except Exception as e:
            logger.error(f"处理第{page_num}页时发生异常: {e}")
            continue
    
    # 3. 输出汇总统计信息
    print("\n📊 多页处理统计:")
    print("=" * 50)
    print(f"📄 总页数: {len(pages)}")
    print(f"🔬 总检验项目数: {total_items}")
    print(f"📊 平均每页项目数: {total_items/len(pages):.1f}" if pages else "0")
    print("=" * 50)

    # 4. 输出坐标信息日志（如果启用了坐标提取）
    if ocr_blocks and all_results:
        print("\n📍 坐标信息日志:")
        print("=" * 50)
        coordinate_log = {
            "words_block_list": []
        }

        items_with_coordinates = [item for item in all_results if hasattr(item, 'location') and item.location]
        print(f"包含坐标信息的项目数: {len(items_with_coordinates)}")

        for item in items_with_coordinates:
            if item.location:
                coordinate_entry = {
                    "words": item.test_name,
                    "location": item.location
                }
                coordinate_log["words_block_list"].append(coordinate_entry)

                # 输出格式化的坐标信息
                print(f"  {item.test_name}: {item.location}")

        # 输出JSON格式的坐标日志
        import json
        print("\n📋 JSON格式坐标日志:")
        print(json.dumps(coordinate_log, ensure_ascii=False, indent=2))
        print("=" * 50)

    # 5. 输出最终结果表格
    if all_results:
        print("\n检验项目识别结果（所有页面汇总）:")
        headers = ["页码", "检查代码", "检查名称", "检查结果值", "异常符号", "检查单位", "参考范围"]
        table_data = []
        for item in all_results:
            print(item)
            table_data.append([
                item.page_num or "",
                item.test_code or "",
                item.test_name or "",
                item.test_value or "",
                item.abnormal_symbol or "",
                item.test_unit or "",
                item.reference_value or ""
            ])
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
    
    logger.info(f"医疗检验单处理完成，总共获得 {total_items} 个检验项目")
    return all_results





if __name__ == "__main__":
    t = """[SMO-2022-6673-镇江市第三人民医院-000821-生命体征表.pdf]
[Page 1]
H206_葡萄糖（GLU) 华中科技大学同济医学院附属同济医院检验科报告单
姓 名：卢翔 类科病 别：临床试验 年龄：39岁 性 别男 样本编号：20240507G0028343
病人ID:2417 室： 送检医生：侯子君 条码号：1282281789 备注：
标本：血 区： 床号： 临床诊断：
检验项目 结果 提示参考区间 单位
葡萄糖 4.90 3.90-6.10 7/10m
号
XJ
2024.5.7
采样时间：2024-05-0712:16 接收时间：2024-05-07 检验者： 审核者：辉军
审核时间：2024-05-0713:41
联系地址：湖北省武汉市解放大道1095号 联系电话：027-83662916 注：本报告仅对送检标本负责！

[SMO-2022-6673-镇江市第三人民医院-000821-生命体征表.pdf]
[Page 2]
杭州金域医学检验所有限公司
KingMed Diagnostics(HANGZHOU)
金域医学 检验结果报告单 4905
so pe
标本条码 1002549723 医院 浙江省肿瘤医院
科室 （妇瘤放疗科2） 单位
姓名 周敏 108 实验号 220315ADK00046
性别 女 住院/门诊号 00254772 送检标本
年龄 59岁 房/床号 标本情况
联系电话 15988278340 申请医生 吴婉莉 采样时间 2022.03.1508:07
临床诊断 肺占位性病变，宫颈恶性 医院标识
测定结果 单位 接收时间 2022.03,1509:15
中文名称 参考区间 方法 仪器
1脂肪酶（LIP） 58.2 U/L 13.0-60.0 酶法
李花 2020.3.18
测试时间：2024-05-0712:16 打印时间：2024年5月6日 15:10:11

[SMO-2022-6673-镇江市第三人民医院-000821-生命体征表.pdf]
[Page 3]
"""
    process_medical_ocr(t)








