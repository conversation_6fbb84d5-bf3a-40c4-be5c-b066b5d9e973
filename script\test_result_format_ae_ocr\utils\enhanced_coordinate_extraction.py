"""
增强版坐标提取工具：专门处理双栏表格布局

该模块针对双栏表格布局的复杂情况进行了优化，主要改进包括：
1. 精确定位策略：基于test_code/test_name进行精确匹配
2. 列边界识别：分析X坐标分布，识别表格列结构
3. 智能行分组：结合line_break标记和列边界进行精确分割
4. 双栏布局支持：准确处理一行包含多个检验项的情况

使用方法：
    from script.test_result_format_ae_ocr.utils.enhanced_coordinate_extraction import extract_dual_column_coordinates
    
    # 提取双栏表格的坐标
    item_coordinates = extract_dual_column_coordinates(test_items, ocr_data)
"""

import re
import logging
from typing import List, Dict, Tuple, Optional, Any, Set
from dataclasses import dataclass
from difflib import SequenceMatcher
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class ItemCoordinate:
    """单个检验项目的坐标信息"""
    item_index: int                         # 项目索引
    test_code: str                         # 测试代码
    test_name: str                         # 测试名称
    location_points: List[List[float]]     # 四个坐标点：[[左上x,左上y], [右上x,右上y], [右下x,右下y], [左下x,左下y]]
    row_index: int                         # 所在行索引
    column_index: int                      # 所在列索引（0=第一列，1=第二列）
    anchor_block_index: int                # 锚点块索引（test_code或test_name匹配的块）
    matched_blocks: List[int]              # 所有匹配的块索引
    confidence: float                      # 匹配置信度


@dataclass
class ColumnBoundary:
    """列边界信息"""
    column_index: int                      # 列索引
    x_start: float                        # 列开始X坐标
    x_end: float                          # 列结束X坐标
    x_center: float                       # 列中心X坐标
    width: float                          # 列宽度


class EnhancedCoordinateMapper:
    """增强版坐标映射器：专门处理双栏表格布局"""
    
    def __init__(self, exact_match_threshold: float = 0.9, fuzzy_match_threshold: float = 0.7):
        """
        初始化增强版坐标映射器
        
        Args:
            exact_match_threshold: 精确匹配阈值（用于test_code/test_name）
            fuzzy_match_threshold: 模糊匹配阈值（用于其他字段）
        """
        self.exact_match_threshold = exact_match_threshold
        self.fuzzy_match_threshold = fuzzy_match_threshold
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0.0
        
        # 清理文本：移除空格和特殊字符
        clean_text1 = re.sub(r'\s+', '', text1.lower())
        clean_text2 = re.sub(r'\s+', '', text2.lower())
        
        # 使用序列匹配器计算相似度
        return SequenceMatcher(None, clean_text1, clean_text2).ratio()
    
    def find_anchor_block(self, test_item, ocr_blocks: List[Dict[str, Any]]) -> Optional[Tuple[int, float, str]]:
        """
        查找检验项目的锚点块（test_code或test_name的最佳匹配）
        
        Args:
            test_item: 测试项目对象
            ocr_blocks: OCR文本块列表
            
        Returns:
            (块索引, 相似度, 匹配字段) 或 None
        """
        best_match = None
        best_similarity = 0.0
        best_field = ""
        
        # 优先匹配test_code（通常更唯一）
        if hasattr(test_item, 'test_code') and test_item.test_code:
            for i, block in enumerate(ocr_blocks):
                block_text = block.get('words', '')
                similarity = self.calculate_text_similarity(test_item.test_code, block_text)
                
                if similarity >= self.exact_match_threshold and similarity > best_similarity:
                    best_match = i
                    best_similarity = similarity
                    best_field = "test_code"
        
        # 如果test_code匹配不够好，尝试test_name
        if best_similarity < self.exact_match_threshold and hasattr(test_item, 'test_name') and test_item.test_name:
            for i, block in enumerate(ocr_blocks):
                block_text = block.get('words', '')
                similarity = self.calculate_text_similarity(test_item.test_name, block_text)
                
                if similarity >= self.exact_match_threshold and similarity > best_similarity:
                    best_match = i
                    best_similarity = similarity
                    best_field = "test_name"
        
        if best_match is not None:
            return (best_match, best_similarity, best_field)
        
        return None
    
    def find_related_blocks_in_row(self, anchor_index: int, test_item, ocr_blocks: List[Dict[str, Any]], 
                                  row_blocks: List[int]) -> List[int]:
        """
        在同一行中查找与检验项目相关的其他块
        
        Args:
            anchor_index: 锚点块索引
            test_item: 测试项目对象
            ocr_blocks: OCR文本块列表
            row_blocks: 该行的所有块索引
            
        Returns:
            相关块索引列表
        """
        related_blocks = [anchor_index]
        
        # 获取锚点块的X坐标
        anchor_block = ocr_blocks[anchor_index]
        anchor_location = anchor_block.get('location', [])
        if not anchor_location or len(anchor_location) != 4:
            return related_blocks
        
        anchor_x = sum(point[0] for point in anchor_location) / 4  # 中心X坐标
        
        # 在同一行中查找相关的块（基于X坐标邻近性和文本匹配）
        search_texts = []
        if hasattr(test_item, 'test_value') and test_item.test_value:
            search_texts.append(test_item.test_value)
        if hasattr(test_item, 'test_unit') and test_item.test_unit:
            search_texts.append(test_item.test_unit)
        if hasattr(test_item, 'reference_value') and test_item.reference_value:
            search_texts.append(test_item.reference_value)
        
        for block_idx in row_blocks:
            if block_idx == anchor_index or block_idx in related_blocks:
                continue
            
            block = ocr_blocks[block_idx]
            block_text = block.get('words', '')
            block_location = block.get('location', [])
            
            if not block_location or len(block_location) != 4:
                continue
            
            block_x = sum(point[0] for point in block_location) / 4
            
            # 检查是否在合理的X坐标范围内（同一列或相邻列）
            x_distance = abs(block_x - anchor_x)
            if x_distance > 300:  # 超过300像素认为是不同列的项目
                continue
            
            # 检查文本匹配
            for search_text in search_texts:
                similarity = self.calculate_text_similarity(search_text, block_text)
                if similarity >= self.fuzzy_match_threshold:
                    related_blocks.append(block_idx)
                    break
        
        return related_blocks


class ColumnBoundaryAnalyzer:
    """列边界分析器：分析表格的列结构"""
    
    def __init__(self, min_column_width: float = 100.0):
        """
        初始化列边界分析器
        
        Args:
            min_column_width: 最小列宽度（像素）
        """
        self.min_column_width = min_column_width
    
    def analyze_column_boundaries(self, ocr_blocks: List[Dict[str, Any]]) -> List[ColumnBoundary]:
        """
        分析OCR块的X坐标分布，识别列边界
        
        Args:
            ocr_blocks: OCR文本块列表
            
        Returns:
            列边界信息列表
        """
        if not ocr_blocks:
            return []
        
        # 收集所有块的X坐标
        x_coordinates = []
        for block in ocr_blocks:
            location = block.get('location', [])
            if location and len(location) == 4:
                # 计算块的左边界和右边界
                x_coords = [point[0] for point in location]
                x_min, x_max = min(x_coords), max(x_coords)
                x_center = (x_min + x_max) / 2
                x_coordinates.append((x_min, x_center, x_max))
        
        if not x_coordinates:
            return []
        
        # 分析X坐标分布，识别列边界
        x_centers = [x_center for x_min, x_center, x_max in x_coordinates]
        x_centers.sort()
        
        # 使用聚类方法识别列中心
        column_centers = []
        current_cluster = [x_centers[0]]
        
        for x in x_centers[1:]:
            if x - current_cluster[-1] <= 50:  # 50像素内认为是同一列
                current_cluster.append(x)
            else:
                # 新列
                cluster_center = sum(current_cluster) / len(current_cluster)
                column_centers.append(cluster_center)
                current_cluster = [x]
        
        # 添加最后一个聚类
        if current_cluster:
            cluster_center = sum(current_cluster) / len(current_cluster)
            column_centers.append(cluster_center)
        
        # 生成列边界
        boundaries = []
        overall_x_min = min(x_min for x_min, _, _ in x_coordinates)
        overall_x_max = max(x_max for _, _, x_max in x_coordinates)
        
        if len(column_centers) == 1:
            # 单列布局
            boundaries.append(ColumnBoundary(0, overall_x_min, overall_x_max, column_centers[0], overall_x_max - overall_x_min))
        else:
            # 多列布局
            for i, center in enumerate(column_centers):
                if i == 0:
                    x_start = overall_x_min
                    x_end = (center + column_centers[i + 1]) / 2 if i + 1 < len(column_centers) else overall_x_max
                elif i == len(column_centers) - 1:
                    x_start = (column_centers[i - 1] + center) / 2
                    x_end = overall_x_max
                else:
                    x_start = (column_centers[i - 1] + center) / 2
                    x_end = (center + column_centers[i + 1]) / 2
                
                width = x_end - x_start
                boundaries.append(ColumnBoundary(i, x_start, x_end, center, width))
        
        return boundaries
    
    def determine_block_column(self, block_location: List[List[float]], 
                              column_boundaries: List[ColumnBoundary]) -> int:
        """
        确定文本块属于哪一列
        
        Args:
            block_location: 文本块的四个坐标点
            column_boundaries: 列边界信息列表
            
        Returns:
            列索引（0开始）
        """
        if not block_location or len(block_location) != 4 or not column_boundaries:
            return 0
        
        # 计算块的中心X坐标
        x_coords = [point[0] for point in block_location]
        block_x_center = sum(x_coords) / len(x_coords)
        
        # 找到最接近的列
        best_column = 0
        min_distance = float('inf')
        
        for boundary in column_boundaries:
            distance = abs(block_x_center - boundary.x_center)
            if distance < min_distance:
                min_distance = distance
                best_column = boundary.column_index
        
        return best_column


class EnhancedRowExtractor:
    """增强版行提取器：支持双栏表格布局"""
    
    def __init__(self):
        """初始化增强版行提取器"""
        self.mapper = EnhancedCoordinateMapper()
        self.column_analyzer = ColumnBoundaryAnalyzer()
    
    def extract_all_rows_with_line_break(self, ocr_blocks: List[Dict[str, Any]]) -> List[List[int]]:
        """
        使用line_break标记提取所有行的块索引
        
        Args:
            ocr_blocks: OCR文本块列表
            
        Returns:
            按行分组的所有块索引列表
        """
        rows = []
        current_row = []
        
        for i, block in enumerate(ocr_blocks):
            current_row.append(i)
            
            # 检查是否有line_break标记
            if block.get('line_break', False):
                # 遇到line_break，结束当前行
                if current_row:
                    rows.append(current_row)
                    current_row = []
        
        # 添加最后一行（如果有剩余的块）
        if current_row:
            rows.append(current_row)
        
        return rows
    
    def calculate_item_coordinates(self, test_item, item_index: int, ocr_blocks: List[Dict[str, Any]], 
                                 row_blocks: List[int], column_boundaries: List[ColumnBoundary]) -> Optional[ItemCoordinate]:
        """
        计算单个检验项目的精确坐标
        
        Args:
            test_item: 测试项目对象
            item_index: 项目索引
            ocr_blocks: OCR文本块列表
            row_blocks: 该行的所有块索引
            column_boundaries: 列边界信息
            
        Returns:
            项目坐标信息或None
        """
        # 查找锚点块
        anchor_result = self.mapper.find_anchor_block(test_item, ocr_blocks)
        if not anchor_result:
            logger.warning(f"项目 {item_index} 未找到锚点块")
            return None
        
        anchor_index, anchor_similarity, anchor_field = anchor_result
        
        # 确保锚点块在当前行中
        if anchor_index not in row_blocks:
            logger.warning(f"项目 {item_index} 的锚点块不在当前行中")
            return None
        
        # 确定锚点块所在的列
        anchor_block = ocr_blocks[anchor_index]
        anchor_location = anchor_block.get('location', [])
        column_index = self.column_analyzer.determine_block_column(anchor_location, column_boundaries)
        
        # 查找该项目在当前行中的相关块
        related_blocks = self.mapper.find_related_blocks_in_row(
            anchor_index, test_item, ocr_blocks, row_blocks
        )
        
        # 过滤：只保留同一列的块
        if column_boundaries:
            column_boundary = column_boundaries[column_index] if column_index < len(column_boundaries) else None
            if column_boundary:
                filtered_blocks = []
                for block_idx in related_blocks:
                    block = ocr_blocks[block_idx]
                    block_location = block.get('location', [])
                    block_column = self.column_analyzer.determine_block_column(block_location, column_boundaries)
                    if block_column == column_index:
                        filtered_blocks.append(block_idx)
                related_blocks = filtered_blocks
        
        if not related_blocks:
            related_blocks = [anchor_index]
        
        # 计算项目的坐标范围
        all_x_coords = []
        all_y_coords = []
        total_confidence = 0.0
        
        for block_idx in related_blocks:
            block = ocr_blocks[block_idx]
            location = block.get('location', [])
            confidence = block.get('confidence', 1.0)
            
            if location and len(location) == 4:
                for point in location:
                    all_x_coords.append(point[0])
                    all_y_coords.append(point[1])
                total_confidence += confidence
        
        if not all_x_coords or not all_y_coords:
            return None
        
        # 计算边界坐标
        x_min, x_max = min(all_x_coords), max(all_x_coords)
        y_min, y_max = min(all_y_coords), max(all_y_coords)
        
        # 生成四个坐标点
        location_points = [
            [x_min, y_min],  # 左上
            [x_max, y_min],  # 右上
            [x_max, y_max],  # 右下
            [x_min, y_max]   # 左下
        ]
        
        # 计算平均置信度
        avg_confidence = total_confidence / len(related_blocks) if related_blocks else 0.0
        
        return ItemCoordinate(
            item_index=item_index,
            test_code=getattr(test_item, 'test_code', ''),
            test_name=getattr(test_item, 'test_name', ''),
            location_points=location_points,
            row_index=-1,  # 稍后设置
            column_index=column_index,
            anchor_block_index=anchor_index,
            matched_blocks=related_blocks,
            confidence=avg_confidence
        )


def extract_dual_column_coordinates(test_items: List[Any], ocr_data: List[Dict[str, Any]], 
                                  exact_match_threshold: float = 0.9) -> List[ItemCoordinate]:
    """
    提取双栏表格布局的检验项目坐标
    
    Args:
        test_items: 结构化的测试项目列表
        ocr_data: OCR数据列表，格式为 [{"page": int, "words_block_list": [...]}]
        exact_match_threshold: 精确匹配阈值
        
    Returns:
        检验项目坐标信息列表
    """
    logger.info(f"开始提取双栏表格坐标，测试项目数: {len(test_items)}, OCR页数: {len(ocr_data)}")
    
    if not test_items or not ocr_data:
        logger.warning("测试项目或OCR数据为空，无法提取坐标")
        return []
    
    # 展开所有页面的OCR文本块
    all_ocr_blocks = []
    for page_data in ocr_data:
        words_block_list = page_data.get("words_block_list", [])
        all_ocr_blocks.extend(words_block_list)
    
    logger.info(f"展开后OCR块总数: {len(all_ocr_blocks)}")
    
    # 创建增强版提取器
    extractor = EnhancedRowExtractor()
    
    # 第一步：分析列边界
    column_boundaries = extractor.column_analyzer.analyze_column_boundaries(all_ocr_blocks)
    logger.info(f"识别到 {len(column_boundaries)} 列")
    for i, boundary in enumerate(column_boundaries):
        logger.info(f"  列 {i}: X坐标 {boundary.x_start:.1f} - {boundary.x_end:.1f} (中心: {boundary.x_center:.1f})")
    
    # 第二步：提取所有行
    all_rows = extractor.extract_all_rows_with_line_break(all_ocr_blocks)
    logger.info(f"识别到 {len(all_rows)} 行")
    
    # 第三步：为每个测试项目查找坐标
    item_coordinates = []
    
    for item_index, test_item in enumerate(test_items):
        best_coordinate = None
        best_row_index = -1
        
        # 在所有行中查找该项目的最佳匹配
        for row_index, row_blocks in enumerate(all_rows):
            item_coord = extractor.calculate_item_coordinates(
                test_item, item_index, all_ocr_blocks, row_blocks, column_boundaries
            )
            
            if item_coord and (best_coordinate is None or item_coord.confidence > best_coordinate.confidence):
                best_coordinate = item_coord
                best_row_index = row_index
        
        if best_coordinate:
            best_coordinate.row_index = best_row_index
            item_coordinates.append(best_coordinate)
            logger.debug(f"项目 {item_index} ({best_coordinate.test_name}) 定位到行 {best_row_index} 列 {best_coordinate.column_index}")
        else:
            logger.warning(f"项目 {item_index} 未找到坐标信息")
    
    # 按行和列排序
    item_coordinates.sort(key=lambda x: (x.row_index, x.column_index))
    
    logger.info(f"双栏表格坐标提取完成，成功定位 {len(item_coordinates)} 个项目")
    return item_coordinates


def apply_enhanced_coordinates_to_items(test_items: List[Any], item_coordinates: List[ItemCoordinate]):
    """
    将增强的坐标信息应用到测试项目中

    Args:
        test_items: 测试项目列表
        item_coordinates: 项目坐标信息列表
    """
    # 创建索引映射
    coord_map = {coord.item_index: coord for coord in item_coordinates}

    # 应用坐标信息
    for i, test_item in enumerate(test_items):
        if i in coord_map:
            coord = coord_map[i]
            test_item.location = coord.location_points
            logger.debug(f"应用坐标到项目 {i}: {coord.location_points}")


def print_dual_column_summary(item_coordinates: List[ItemCoordinate], test_items: List[Any] = None):
    """
    打印双栏表格坐标提取结果摘要
    
    Args:
        item_coordinates: 项目坐标信息列表
        test_items: 测试项目列表（可选）
    """
    if not item_coordinates:
        print("📍 未找到项目坐标信息")
        return
    
    print(f"\n📍 双栏表格坐标提取结果（共 {len(item_coordinates)} 个项目）:")
    print("=" * 100)
    
    # 按行分组显示
    rows = defaultdict(list)
    for coord in item_coordinates:
        rows[coord.row_index].append(coord)
    
    for row_index in sorted(rows.keys()):
        row_items = sorted(rows[row_index], key=lambda x: x.column_index)
        print(f"\n行 {row_index}:")
        
        for coord in row_items:
            x_min, y_min = coord.location_points[0]  # 左上
            x_max, y_max = coord.location_points[2]  # 右下
            print(f"  列 {coord.column_index}: {coord.test_name}")
            print(f"    坐标: ({x_min:.1f},{y_min:.1f}) - ({x_max:.1f},{y_max:.1f})")
            print(f"    置信度: {coord.confidence:.2f}")
            print(f"    锚点块: {coord.anchor_block_index}")
    
    print("=" * 100)
