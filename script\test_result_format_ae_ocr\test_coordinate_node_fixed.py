# -*- coding: utf-8 -*-
"""
修正版CoordinateExtractionNode节点测试脚本

该脚本修正了之前的问题，确保：
1. 直接调用CoordinateExtractionNode节点进行测试
2. 提取完整行的坐标信息，而不仅仅是test_name字段
3. 验证输出的location坐标覆盖整行的所有相关字段
4. 使用真实的大模型输出数据和OCR坐标数据

问题修正：
- 之前的独立实现只匹配单个test_name字段
- 现在使用真正的CoordinateExtractionNode逻辑
- 确保坐标提取是行级别而非字段级别

使用方法:
    conda activate smo-ai-backend
    python test_coordinate_node_fixed.py
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional

current_script_path = os.path.abspath(__file__)
# 获取项目根目录 (假设项目根目录是 d:\Code\smo-ai-backend)
# 向上两级目录，从 script/test_result_format_ae_ocr/test_main.py 到 d:\Code\smo-ai-backend
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    # 确保在调用 django.setup() 之前设置 DJANGO_SETTINGS_MODULE
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')

    import django
    django.setup()
    
    from script.test_result_format_ae_ocr.models import TestItem
    from script.test_result_format_ae_ocr.nodes import CoordinateExtractionNode
    from script.test_result_format_ae_ocr.utils.coordinate_extraction import extract_row_coordinates
    print("✅ 成功导入Django模块")
    DJANGO_AVAILABLE = True
    
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    print("🔄 请确保已激活smo-ai-backend环境，并正确设置数据库连接等配置")
    DJANGO_AVAILABLE = False


class FixedCoordinateNodeTester:
    """修正版CoordinateExtractionNode节点测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_data_dir = Path(__file__).parent / "test_data"
        self.llm_output_data = None
        self.ocr_data = None
        self.test_items = []
        
    def load_test_data(self):
        """加载测试数据"""
        print("📂 加载测试数据...")
        
        # 大模型输出的JSON数据
        llm_output_json = '''[
{"tc":"SG","tn":"*(干化学)比重","tv":"6101","as":"","tu":"1.003-1.030","rf":""},
{"tc":"SPERM","tn":"（尿流式)精子","tv":"0","as":"","tu":"/ul","rf":"0-0"},
{"tc":"GLU","tn":"*(干化学)葡萄糖","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"SRC","tn":"(尿流式)小圆上皮细胞","tv":"0.4","as":"","tu":"n/$","rf":"ε-0"},
{"tc":"PRO","tn":"*(干化学)蛋白","tv":"+","as":"↑","tu":"","rf":"阴性"},
{"tc":"EC","tn":"(尿流式)上皮细胞","tv":"0","as":"↑","tu":"/uL","rf":"0-5"},
{"tc":"PH","tn":"*(干化学)酸度","tv":"7.5","as":"","tu":"4.5-8.0","rf":""},
{"tc":"KET","tn":"*(干化学)酮体","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"UBG","tn":"*(干化学)尿胆原","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"BLD","tn":"*(干化学)潜血","tv":"++","as":"1","tu":"阴性","rf":""},
{"tc":"LEU","tn":"*（干化学）白细胞酯酶","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"NIT","tn":"*(干化学)亚硝酸盐","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"BII.","tn":"*(干化学)胆红素","tv":"阴性","as":"","tu":"阴性","rf":""},
{"tc":"WBC","tn":"(尿流式)白细胞","tv":"27","as":"↑","tu":"/ul","rf":"0-11"},
{"tc":"RBC","tn":"(尿流式)红细胞","tv":"6139","as":"1","tu":"/↓","rf":"0-14"},
{"tc":"","tn":"（尿流式)完整红细胞百分比","tv":"100.0","as":"","tu":"%","rf":""},
{"tc":"BACT","tn":"(尿流式)细菌","tv":"13","as":"","tu":"/uL","rf":"0-51"},
{"tc":"YLC","tn":"(尿流式)酵母样菌","tv":"0","as":"","tu":"/ul.","rf":"0"},
{"tc":"X,TAI.","tn":"（尿流式）结品数量","tv":"0","as":"","tu":"/ul","rf":"0"},
{"tc":"X,TAI.1","tn":"（镜检)结晶类别","tv":"/","as":"","tu":"","rf":""},
{"tc":"MUS","tn":"(尿流式)粘液丝","tv":"0.00","as":"","tu":"/ul","rf":""},
{"tc":"CAST1","tn":"（镜检）管型类别1","tv":"未见","as":"","tu":"个/LPF","rf":""},
{"tc":"CAST2","tn":"(镜检)管型类别2","tv":"","as":"","tu":"个/LPF","rf":""}
]'''
        
        try:
            self.llm_output_data = json.loads(llm_output_json)
            print(f"  ✅ 解析大模型输出: {len(self.llm_output_data)} 个检验项目")
        except json.JSONDecodeError as e:
            print(f"  ❌ JSON解析失败: {e}")
            return False
        
        # 加载OCR数据
        ocr_file = self.test_data_dir / "ocr_test_data.json"
        if not ocr_file.exists():
            print(f"  ❌ OCR数据文件不存在: {ocr_file}")
            return False
        
        try:
            with open(ocr_file, 'r', encoding='utf-8') as f:
                ocr_raw = json.load(f)
            
            # 转换为标准格式
            if isinstance(ocr_raw, dict) and "words_block_list" in ocr_raw:
                self.ocr_data = [ocr_raw]
            else:
                self.ocr_data = ocr_raw
            
            total_blocks = sum(len(page.get("words_block_list", [])) for page in self.ocr_data)
            print(f"  ✅ 加载OCR数据: {len(self.ocr_data)} 页, {total_blocks} 个文本块")
            return True
            
        except Exception as e:
            print(f"  ❌ OCR数据加载失败: {e}")
            return False
    
    def convert_to_test_items(self):
        """将大模型输出转换为TestItem对象"""
        print("\n🔄 转换大模型输出为TestItem对象...")
        
        if not DJANGO_AVAILABLE:
            print("  ❌ Django环境不可用，无法创建TestItem对象")
            return False
        
        try:
            self.test_items = []
            
            for i, item_data in enumerate(self.llm_output_data):
                test_item = TestItem(
                    test_code=item_data.get("tc", ""),
                    test_name=item_data.get("tn", ""),
                    test_value=item_data.get("tv", ""),
                    test_flag=0,  # 默认正常
                    test_type="text",  # 默认定性/文本型
                    test_unit=item_data.get("tu", ""),
                    reference_value=item_data.get("rf", ""),
                    abnormal_symbol=item_data.get("as", ""),
                    page_num=1
                )
                self.test_items.append(test_item)
            
            print(f"  ✅ 转换完成: {len(self.test_items)} 个TestItem对象")
            return True
            
        except Exception as e:
            print(f"  ❌ 转换失败: {e}")
            return False
    
    def test_coordinate_extraction_node_real(self):
        """使用真实的CoordinateExtractionNode进行测试"""
        print("\n🧪 测试真实的CoordinateExtractionNode节点...")
        print("-" * 60)
        
        if not DJANGO_AVAILABLE:
            print("  ❌ Django环境不可用，无法测试真实节点")
            return {'success': False, 'error': 'Django环境不可用'}
        
        if not self.test_items or not self.ocr_data:
            print("  ❌ 测试数据不完整")
            return {'success': False, 'error': '测试数据不完整'}
        
        try:
            # 创建CoordinateExtractionNode
            coord_node = CoordinateExtractionNode()
            
            print(f"  📊 输入数据:")
            print(f"    测试项目数: {len(self.test_items)}")
            print(f"    OCR页数: {len(self.ocr_data)}")
            
            # 执行坐标提取
            start_time = time.time()
            
            # 准备输入数据
            input_data = {
                "processed_items": self.test_items,
                "ocr_blocks": self.ocr_data
            }
            
            # 按照 PocketFlow 节点调用约定：prep -> exec -> post，并通过 shared 传递数据
            shared = dict(input_data)  # 作为 shared 容器
            prep_result = coord_node.prep(shared)
            exec_result = coord_node.exec(prep_result)
            # 将结果写回 shared，并更新 TestItem 的 location
            coord_node.post(shared, prep_result, exec_result)
            
            processing_time = time.time() - start_time
            
            print(f"  ✅ CoordinateExtractionNode执行完成，用时: {processing_time:.3f}秒")
            
            # 检查结果（从 shared 中读取）
            row_coordinates = shared.get("row_coordinates", [])
            print(f"  📍 提取到 {len(row_coordinates)} 行坐标")
            
            # 检查TestItem对象是否被更新了坐标信息
            items_with_location = [item for item in self.test_items if hasattr(item, 'location') and item.location]
            print(f"  📍 包含坐标信息的项目: {len(items_with_location)} 个")
            
            # 显示行坐标详情
            if row_coordinates:
                print("\n  📊 行坐标详情:")
                for i, row_coord in enumerate(row_coordinates):
                    print(f"    行 {i}: Y坐标 {row_coord.y_min:.1f}-{row_coord.y_max:.1f}, X坐标 {row_coord.x_min:.1f}-{row_coord.x_max:.1f}")
                    print(f"      关联项目索引: {row_coord.test_item_indices}")
                    print(f"      匹配块数: {len(row_coord.matched_blocks)}")
                    print(f"      置信度: {row_coord.confidence:.2f}")
            
            # 显示项目坐标信息
            if items_with_location:
                print("\n  📋 项目坐标信息:")
                for i, item in enumerate(items_with_location[:5]):
                    if item.location:
                        x_min, y_min = item.location[0]  # 左上
                        x_max, y_max = item.location[2]  # 右下
                        width = x_max - x_min
                        height = y_max - y_min
                        print(f"    {i+1}. {item.test_name}")
                        print(f"       坐标: ({x_min:.1f},{y_min:.1f}) - ({x_max:.1f},{y_max:.1f})")
                        print(f"       尺寸: {width:.1f} x {height:.1f}")
            
            return {
                'success': True,
                'processing_time': processing_time,
                'row_coordinates': row_coordinates,
                'items_with_location': items_with_location
            }
            
        except Exception as e:
            print(f"  ❌ CoordinateExtractionNode测试失败: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': str(e)}
    
    def analyze_row_vs_field_coordinates(self, test_result):
        """分析行级坐标vs字段级坐标的差异"""
        print("\n📊 分析行级坐标vs字段级坐标...")
        print("-" * 60)
        
        if not test_result['success']:
            print("  ⚠️ 跳过分析：坐标提取未成功")
            return
        
        row_coordinates = test_result.get('row_coordinates', [])
        items_with_location = test_result.get('items_with_location', [])
        
        if not row_coordinates or not items_with_location:
            print("  ⚠️ 无坐标数据可分析")
            return
        
        print(f"  📊 坐标范围分析:")
        print(f"    行坐标数: {len(row_coordinates)}")
        print(f"    项目坐标数: {len(items_with_location)}")
        
        # 分析每行的坐标范围
        for i, row_coord in enumerate(row_coordinates):
            print(f"\n    行 {i}:")
            print(f"      行坐标范围: X({row_coord.x_min:.1f}-{row_coord.x_max:.1f}) Y({row_coord.y_min:.1f}-{row_coord.y_max:.1f})")
            print(f"      行尺寸: {row_coord.width:.1f} x {row_coord.height:.1f}")
            print(f"      关联项目: {row_coord.test_item_indices}")
            
            # 显示该行关联项目的坐标
            for item_idx in row_coord.test_item_indices:
                if item_idx < len(self.test_items):
                    item = self.test_items[item_idx]
                    if hasattr(item, 'location') and item.location:
                        item_x_min, item_y_min = item.location[0]
                        item_x_max, item_y_max = item.location[2]
                        print(f"        项目 {item_idx} ({item.test_name}): X({item_x_min:.1f}-{item_x_max:.1f}) Y({item_y_min:.1f}-{item_y_max:.1f})")
                        
                        # 验证项目坐标是否与行坐标一致
                        if (abs(item_x_min - row_coord.x_min) < 1 and 
                            abs(item_x_max - row_coord.x_max) < 1 and
                            abs(item_y_min - row_coord.y_min) < 1 and
                            abs(item_y_max - row_coord.y_max) < 1):
                            print(f"          ✅ 坐标与行坐标一致（行级坐标）")
                        else:
                            print(f"          ⚠️ 坐标与行坐标不一致（可能是字段级坐标）")
    
    def generate_output_format(self, test_result):
        """生成期望的输出格式"""
        print("\n📋 生成输出格式...")
        print("-" * 60)
        
        if not test_result['success']:
            print("  ⚠️ 跳过输出格式生成：测试未成功")
            return None
        
        items_with_location = test_result.get('items_with_location', [])
        
        if not items_with_location:
            print("  ⚠️ 无坐标信息，无法生成输出格式")
            return None
        
        # 生成words_block_list格式的输出
        words_block_list = []
        
        for item in items_with_location:
            if item.location:
                block_entry = {
                    "words": item.test_name,
                    "confidence": 0.96,  # 默认置信度
                    "location": item.location
                }
                words_block_list.append(block_entry)
        
        output_data = {
            "words_block_list": words_block_list
        }
        
        print(f"  📊 生成输出格式:")
        print(f"    项目数: {len(words_block_list)}")
        
        # 显示前几个项目的详细信息
        print("  📋 前3个项目的坐标信息:")
        for i, block in enumerate(words_block_list[:3]):
            print(f"    {i+1}. {block['words']}")
            print(f"       置信度: {block['confidence']}")
            print(f"       坐标: {block['location']}")
            
            # 计算坐标范围
            x_coords = [point[0] for point in block['location']]
            y_coords = [point[1] for point in block['location']]
            width = max(x_coords) - min(x_coords)
            height = max(y_coords) - min(y_coords)
            print(f"       范围: ({min(x_coords):.1f},{min(y_coords):.1f}) - ({max(x_coords):.1f},{max(y_coords):.1f})")
            print(f"       尺寸: {width:.1f} x {height:.1f}")
        
        # 输出完整JSON
        print("\n  📄 完整JSON输出:")
        print(json.dumps(output_data, ensure_ascii=False, indent=2))
        
        return output_data
    
    def validate_row_level_coordinates(self, test_result):
        """验证是否为行级坐标"""
        print("\n✅ 验证行级坐标...")
        print("-" * 60)
        
        if not test_result['success']:
            print("  ⚠️ 跳过验证：测试未成功")
            return False
        
        row_coordinates = test_result.get('row_coordinates', [])
        items_with_location = test_result.get('items_with_location', [])
        
        if not row_coordinates:
            print("  ⚠️ 无行坐标数据")
            return False
        
        print(f"  📊 行级坐标验证:")
        
        row_level_count = 0
        field_level_count = 0
        
        for row_coord in row_coordinates:
            print(f"\n    行 {row_coord.row_index}:")
            print(f"      行坐标: X({row_coord.x_min:.1f}-{row_coord.x_max:.1f}) Y({row_coord.y_min:.1f}-{row_coord.y_max:.1f})")
            print(f"      行宽度: {row_coord.width:.1f}")
            
            # 检查该行关联的项目坐标
            for item_idx in row_coord.test_item_indices:
                if item_idx < len(self.test_items):
                    item = self.test_items[item_idx]
                    if hasattr(item, 'location') and item.location:
                        item_x_min, item_y_min = item.location[0]
                        item_x_max, item_y_max = item.location[2]
                        item_width = item_x_max - item_x_min
                        
                        print(f"        项目 {item_idx} ({item.test_name}): 宽度 {item_width:.1f}")
                        
                        # 判断是行级坐标还是字段级坐标
                        if item_width > 200:  # 宽度大于200像素认为是行级坐标
                            row_level_count += 1
                            print(f"          ✅ 行级坐标（宽度: {item_width:.1f}）")
                        else:
                            field_level_count += 1
                            print(f"          ⚠️ 字段级坐标（宽度: {item_width:.1f}）")
        
        print(f"\n  📊 坐标类型统计:")
        print(f"    行级坐标项目: {row_level_count}")
        print(f"    字段级坐标项目: {field_level_count}")
        print(f"    行级坐标比例: {row_level_count/(row_level_count+field_level_count)*100:.1f}%")
        
        # 验证是否主要是行级坐标
        is_row_level = row_level_count > field_level_count
        
        if is_row_level:
            print("  ✅ 验证通过：主要提取的是行级坐标")
        else:
            print("  ⚠️ 验证警告：主要提取的是字段级坐标")
        
        return is_row_level


def main():
    """主函数"""
    print("🚀 修正版CoordinateExtractionNode节点测试")
    print("=" * 80)
    
    if not DJANGO_AVAILABLE:
        print("❌ Django环境不可用")
        print("请使用以下命令激活环境：")
        print("conda activate smo-ai-backend")
        return False
    
    # 创建测试器
    tester = FixedCoordinateNodeTester()
    
    # 加载测试数据
    if not tester.load_test_data():
        print("❌ 测试数据加载失败")
        return False
    
    # 转换数据格式
    if not tester.convert_to_test_items():
        print("❌ 数据格式转换失败")
        return False
    
    # 测试真实的CoordinateExtractionNode
    test_result = tester.test_coordinate_extraction_node_real()
    
    if not test_result['success']:
        print("❌ CoordinateExtractionNode测试失败")
        return False
    
    # 分析行级vs字段级坐标
    tester.analyze_row_vs_field_coordinates(test_result)
    
    # 验证行级坐标
    is_row_level = tester.validate_row_level_coordinates(test_result)
    
    # 生成输出格式
    output_data = tester.generate_output_format(test_result)
    
    # 生成最终报告
    print("\n📊 测试总结")
    print("=" * 80)
    
    print(f"📈 测试结果:")
    print(f"  CoordinateExtractionNode: {'✅ 成功' if test_result['success'] else '❌ 失败'}")
    print(f"  行级坐标验证: {'✅ 通过' if is_row_level else '⚠️ 警告'}")
    print(f"  输出格式生成: {'✅ 成功' if output_data else '❌ 失败'}")
    
    if test_result['success'] and is_row_level and output_data:
        print("\n🎉 修正版CoordinateExtractionNode节点测试完全成功！")
        print("✅ 确认提取的是行级坐标，覆盖整行检验项信息")
        return True
    else:
        print("\n⚠️ 测试中发现问题，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
