# -*- coding: utf-8 -*-
"""
双栏表格布局坐标提取测试脚本

该脚本专门测试双栏表格布局的坐标提取功能，包括：
1. 精确定位策略测试
2. 列边界识别测试
3. 双栏布局处理测试
4. 与原有方案的对比测试

使用方法:
    python test_dual_column_layout.py
"""

import sys
import os
from pathlib import Path

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入增强版坐标提取功能
from script.test_result_format_ae_ocr.utils.enhanced_coordinate_extraction import (
    extract_dual_column_coordinates,
    apply_enhanced_coordinates_to_items,
    print_dual_column_summary,
    ColumnBoundaryAnalyzer,
    EnhancedCoordinateMapper
)

# 简化的TestItem类，用于测试
class SimpleTestItem:
    """简化的测试项目类，用于独立测试"""
    def __init__(self, test_code="", test_name="", test_value="", test_unit="", reference_value=""):
        self.test_code = test_code
        self.test_name = test_name
        self.test_value = test_value
        self.test_unit = test_unit
        self.reference_value = reference_value
        self.row_coordinates = None


def create_dual_column_test_data():
    """创建双栏表格测试数据"""
    # 模拟双栏表格的测试项目
    test_items = [
        SimpleTestItem(
            test_code="GLU",
            test_name="葡萄糖",
            test_value="4.90",
            test_unit="mmol/L",
            reference_value="3.90-6.10"
        ),
        SimpleTestItem(
            test_code="CHOL",
            test_name="胆固醇",
            test_value="5.2",
            test_unit="mmol/L",
            reference_value="3.0-5.0"
        ),
        SimpleTestItem(
            test_code="TG",
            test_name="甘油三酯",
            test_value="1.8",
            test_unit="mmol/L",
            reference_value="0.5-1.7"
        ),
        SimpleTestItem(
            test_code="HDL",
            test_name="高密度脂蛋白",
            test_value="1.2",
            test_unit="mmol/L",
            reference_value="1.0-2.0"
        )
    ]
    
    # 双栏表格的OCR数据：两行，每行两个检验项
    ocr_data = [
        {
            "page": 1,
            "words_block_list": [
                # 表头
                {"words": "检验项目", "location": [[50, 150], [120, 150], [120, 170], [50, 170]], "confidence": 0.95},
                {"words": "结果", "location": [[150, 150], [180, 150], [180, 170], [150, 170]], "confidence": 0.95},
                {"words": "参考区间", "location": [[200, 150], [270, 150], [270, 170], [200, 170]], "confidence": 0.95},
                {"words": "单位", "location": [[300, 150], [330, 150], [330, 170], [300, 170]], "confidence": 0.95},
                {"words": "检验项目", "location": [[400, 150], [470, 150], [470, 170], [400, 170]], "confidence": 0.95},
                {"words": "结果", "location": [[500, 150], [530, 150], [530, 170], [500, 170]], "confidence": 0.95},
                {"words": "参考区间", "location": [[550, 150], [620, 150], [620, 170], [550, 170]], "confidence": 0.95},
                {"words": "单位", "location": [[650, 150], [680, 150], [680, 170], [650, 170]], "confidence": 0.95, "line_break": True},
                
                # 第一行数据：葡萄糖 + 胆固醇
                {"words": "GLU", "location": [[50, 200], [80, 200], [80, 220], [50, 220]], "confidence": 0.95},
                {"words": "葡萄糖", "location": [[85, 200], [135, 200], [135, 220], [85, 220]], "confidence": 0.95},
                {"words": "4.90", "location": [[150, 200], [180, 200], [180, 220], [150, 220]], "confidence": 0.98},
                {"words": "3.90-6.10", "location": [[200, 200], [270, 200], [270, 220], [200, 220]], "confidence": 0.92},
                {"words": "mmol/L", "location": [[300, 200], [350, 200], [350, 220], [300, 220]], "confidence": 0.90},
                
                {"words": "CHOL", "location": [[400, 200], [440, 200], [440, 220], [400, 220]], "confidence": 0.93},
                {"words": "胆固醇", "location": [[445, 200], [495, 200], [495, 220], [445, 220]], "confidence": 0.93},
                {"words": "5.2", "location": [[500, 200], [520, 200], [520, 220], [500, 220]], "confidence": 0.97},
                {"words": "3.0-5.0", "location": [[550, 200], [600, 200], [600, 220], [550, 220]], "confidence": 0.91},
                {"words": "mmol/L", "location": [[650, 200], [700, 200], [700, 220], [650, 220]], "confidence": 0.90, "line_break": True},
                
                # 第二行数据：甘油三酯 + 高密度脂蛋白
                {"words": "TG", "location": [[50, 250], [70, 250], [70, 270], [50, 270]], "confidence": 0.94},
                {"words": "甘油三酯", "location": [[75, 250], [135, 250], [135, 270], [75, 270]], "confidence": 0.96},
                {"words": "1.8", "location": [[150, 250], [170, 250], [170, 270], [150, 270]], "confidence": 0.99},
                {"words": "0.5-1.7", "location": [[200, 250], [250, 250], [250, 270], [200, 270]], "confidence": 0.88},
                {"words": "mmol/L", "location": [[300, 250], [350, 250], [350, 270], [300, 270]], "confidence": 0.90},
                
                {"words": "HDL", "location": [[400, 250], [430, 250], [430, 270], [400, 270]], "confidence": 0.92},
                {"words": "高密度脂蛋白", "location": [[435, 250], [515, 250], [515, 270], [435, 270]], "confidence": 0.94},
                {"words": "1.2", "location": [[500, 250], [520, 250], [520, 270], [500, 270]], "confidence": 0.98},
                {"words": "1.0-2.0", "location": [[550, 250], [600, 250], [600, 270], [550, 270]], "confidence": 0.89},
                {"words": "mmol/L", "location": [[650, 250], [700, 250], [700, 270], [650, 270]], "confidence": 0.90, "line_break": True}
            ]
        }
    ]
    
    return test_items, ocr_data


def test_column_boundary_analysis():
    """测试列边界分析功能"""
    print("🧪 测试列边界分析功能...")
    
    test_items, ocr_data = create_dual_column_test_data()
    
    # 展开OCR块
    all_blocks = []
    for page_data in ocr_data:
        all_blocks.extend(page_data["words_block_list"])
    
    # 分析列边界
    analyzer = ColumnBoundaryAnalyzer()
    column_boundaries = analyzer.analyze_column_boundaries(all_blocks)
    
    print(f"  识别到 {len(column_boundaries)} 列")
    for i, boundary in enumerate(column_boundaries):
        print(f"    列 {i}: X坐标 {boundary.x_start:.1f} - {boundary.x_end:.1f} (中心: {boundary.x_center:.1f}, 宽度: {boundary.width:.1f})")
    
    # 验证结果
    assert len(column_boundaries) == 2, f"应该识别到2列，实际识别到{len(column_boundaries)}列"
    assert column_boundaries[0].x_center < column_boundaries[1].x_center, "第一列应该在第二列左边"
    
    print("  ✅ 列边界分析测试通过")


def test_precise_positioning():
    """测试精确定位策略"""
    print("\n🧪 测试精确定位策略...")
    
    test_items, ocr_data = create_dual_column_test_data()
    
    # 展开OCR块
    all_blocks = []
    for page_data in ocr_data:
        all_blocks.extend(page_data["words_block_list"])
    
    # 测试锚点块查找
    mapper = EnhancedCoordinateMapper()
    
    for i, test_item in enumerate(test_items):
        anchor_result = mapper.find_anchor_block(test_item, all_blocks)
        
        if anchor_result:
            anchor_index, similarity, field = anchor_result
            anchor_block = all_blocks[anchor_index]
            print(f"  项目 {i} ({test_item.test_name}): 锚点块 {anchor_index} '{anchor_block['words']}' (字段: {field}, 相似度: {similarity:.2f})")
            
            # 验证锚点块的准确性
            if field == "test_code":
                assert similarity >= 0.9, f"test_code匹配相似度应该>=0.9，实际{similarity:.2f}"
            elif field == "test_name":
                assert similarity >= 0.9, f"test_name匹配相似度应该>=0.9，实际{similarity:.2f}"
        else:
            print(f"  项目 {i} ({test_item.test_name}): 未找到锚点块")
            assert False, f"项目 {i} 应该找到锚点块"
    
    print("  ✅ 精确定位策略测试通过")


def test_dual_column_extraction():
    """测试双栏表格坐标提取"""
    print("\n🧪 测试双栏表格坐标提取...")
    
    test_items, ocr_data = create_dual_column_test_data()
    
    # 执行增强版坐标提取
    item_coordinates = extract_dual_column_coordinates(test_items, ocr_data)
    
    print(f"  提取到 {len(item_coordinates)} 个项目坐标")
    assert len(item_coordinates) == 4, f"应该提取到4个项目坐标，实际提取到{len(item_coordinates)}个"
    
    # 验证行和列分布
    row_distribution = {}
    column_distribution = {}
    
    for coord in item_coordinates:
        row_distribution[coord.row_index] = row_distribution.get(coord.row_index, 0) + 1
        column_distribution[coord.column_index] = column_distribution.get(coord.column_index, 0) + 1
    
    print(f"  行分布: {row_distribution}")
    print(f"  列分布: {column_distribution}")
    
    # 验证双栏布局：应该有2行，每行2个项目
    assert len(row_distribution) == 2, f"应该有2行，实际有{len(row_distribution)}行"
    assert all(count == 2 for count in row_distribution.values()), "每行应该有2个项目"
    assert len(column_distribution) == 2, f"应该有2列，实际有{len(column_distribution)}列"
    assert all(count == 2 for count in column_distribution.values()), "每列应该有2个项目"
    
    # 验证坐标的合理性
    for coord in item_coordinates:
        x_min, y_min = coord.location_points[0]  # 左上
        x_max, y_max = coord.location_points[2]  # 右下
        
        assert x_max > x_min, f"项目 {coord.item_index} 的X坐标范围无效"
        assert y_max > y_min, f"项目 {coord.item_index} 的Y坐标范围无效"
        assert coord.confidence > 0, f"项目 {coord.item_index} 的置信度应该>0"
        
        print(f"    项目 {coord.item_index} ({coord.test_name}): 行{coord.row_index} 列{coord.column_index}")
        print(f"      坐标: ({x_min:.1f},{y_min:.1f}) - ({x_max:.1f},{y_max:.1f})")
        print(f"      置信度: {coord.confidence:.2f}")
    
    print("  ✅ 双栏表格坐标提取测试通过")
    
    return item_coordinates


def test_coordinate_application():
    """测试坐标信息应用到测试项目"""
    print("\n🧪 测试坐标信息应用...")
    
    test_items, ocr_data = create_dual_column_test_data()
    
    # 提取坐标
    item_coordinates = extract_dual_column_coordinates(test_items, ocr_data)
    
    # 应用坐标到测试项目
    apply_enhanced_coordinates_to_items(test_items, item_coordinates)
    
    # 验证应用结果
    applied_count = 0
    for i, test_item in enumerate(test_items):
        if test_item.row_coordinates:
            applied_count += 1
            print(f"  项目 {i} ({test_item.test_name}): 坐标 {test_item.row_coordinates}")
            
            # 验证坐标格式
            assert len(test_item.row_coordinates) == 4, "应该有4个坐标点"
            assert all(len(point) == 2 for point in test_item.row_coordinates), "每个坐标点应该有2个值"
    
    print(f"  成功应用坐标到 {applied_count} 个项目")
    assert applied_count == len(test_items), f"应该为所有{len(test_items)}个项目应用坐标"
    
    print("  ✅ 坐标信息应用测试通过")


def demo_dual_column_functionality():
    """演示双栏表格功能"""
    print("\n🎯 双栏表格功能演示...")
    print("=" * 80)
    
    test_items, ocr_data = create_dual_column_test_data()
    
    print("📝 测试数据:")
    print(f"  测试项目数: {len(test_items)}")
    for i, item in enumerate(test_items):
        print(f"    {i}: {item.test_code} - {item.test_name} = {item.test_value}")
    
    print(f"  OCR页数: {len(ocr_data)}")
    total_blocks = sum(len(page['words_block_list']) for page in ocr_data)
    print(f"  总OCR块数: {total_blocks}")
    
    # 显示双栏布局结构
    print("\n📊 双栏表格布局:")
    print("  第一行: 葡萄糖 (左列) + 胆固醇 (右列)")
    print("  第二行: 甘油三酯 (左列) + 高密度脂蛋白 (右列)")
    
    # 执行坐标提取
    print("\n🔍 执行增强版坐标提取...")
    item_coordinates = extract_dual_column_coordinates(test_items, ocr_data)
    
    # 显示详细结果
    print_dual_column_summary(item_coordinates, test_items)
    
    # 应用坐标到测试项目
    apply_enhanced_coordinates_to_items(test_items, item_coordinates)
    
    print("\n📋 最终结果验证:")
    for i, test_item in enumerate(test_items):
        if test_item.row_coordinates:
            x_min, y_min = test_item.row_coordinates[0]  # 左上
            x_max, y_max = test_item.row_coordinates[2]  # 右下
            print(f"  {test_item.test_name}: ({x_min:.1f},{y_min:.1f}) - ({x_max:.1f},{y_max:.1f})")
    
    print("\n🎯 双栏表格功能演示完成！")


def main():
    """主函数"""
    print("🚀 双栏表格布局坐标提取测试")
    print("=" * 80)
    
    try:
        # 运行各项测试
        test_column_boundary_analysis()
        test_precise_positioning()
        test_dual_column_extraction()
        test_coordinate_application()
        
        print("\n✅ 所有测试通过！")
        
        # 运行演示
        demo_dual_column_functionality()
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
