# -*- coding: utf-8 -*-
"""
坐标提取功能测试脚本

该脚本用于测试和验证坐标提取功能的正确性，包括：
1. 单元测试：测试各个组件的功能
2. 集成测试：测试完整的坐标提取流程
3. 性能测试：测试坐标提取的性能表现

使用方法:
    python test_coordinate_extraction.py
    python test_coordinate_extraction.py --unit-tests  # 只运行单元测试
    python test_coordinate_extraction.py --integration-tests  # 只运行集成测试
"""

import sys
import os
import unittest
import json
from pathlib import Path

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from script.test_result_format_ae_ocr.utils.coordinate_extraction import (
    CoordinateMapper,
    RowCoordinateExtractor,
    RowCoordinate,
    extract_row_coordinates,
    map_items_to_coordinates,
    get_row_y_coordinates,
    get_row_bounding_boxes,
    print_coordinate_summary,
    export_coordinates_to_json
)
from script.test_result_format_ae_ocr.models import TestItem


class TestCoordinateMapper(unittest.TestCase):
    """测试坐标映射器"""
    
    def setUp(self):
        """设置测试数据"""
        self.mapper = CoordinateMapper(similarity_threshold=0.6)
        
        # 模拟OCR文本块
        self.ocr_blocks = [
            {
                "words": "葡萄糖",
                "location": [[100, 200], [150, 200], [150, 220], [100, 220]],
                "confidence": 0.95
            },
            {
                "words": "4.90",
                "location": [[200, 200], [230, 200], [230, 220], [200, 220]],
                "confidence": 0.98
            },
            {
                "words": "3.90-6.10",
                "location": [[300, 200], [370, 200], [370, 220], [300, 220]],
                "confidence": 0.92
            },
            {
                "words": "mmol/L",
                "location": [[400, 200], [450, 200], [450, 220], [400, 220]],
                "confidence": 0.90
            }
        ]
        
        # 模拟测试项目
        self.test_item = TestItem(
            test_code="GLU",
            test_name="葡萄糖",
            test_value="4.90",
            test_flag=0,
            test_type="数值",
            test_unit="mmol/L",
            reference_value="3.90-6.10"
        )
    
    def test_text_similarity(self):
        """测试文本相似度计算"""
        # 完全匹配
        self.assertEqual(self.mapper.calculate_text_similarity("葡萄糖", "葡萄糖"), 1.0)
        
        # 部分匹配
        similarity = self.mapper.calculate_text_similarity("葡萄糖", "葡萄")
        self.assertGreater(similarity, 0.5)
        
        # 不匹配
        similarity = self.mapper.calculate_text_similarity("葡萄糖", "胆固醇")
        self.assertLess(similarity, 0.3)
    
    def test_find_matching_blocks(self):
        """测试查找匹配的OCR块"""
        matches = self.mapper.find_matching_blocks("葡萄糖", self.ocr_blocks)
        
        # 应该找到匹配的块
        self.assertGreater(len(matches), 0)
        
        # 第一个匹配应该是最相似的
        best_match_idx, best_similarity = matches[0]
        self.assertEqual(self.ocr_blocks[best_match_idx]["words"], "葡萄糖")
        self.assertGreater(best_similarity, 0.9)
    
    def test_map_item_to_blocks(self):
        """测试将测试项目映射到OCR块"""
        matched_indices = self.mapper.map_item_to_blocks(self.test_item, self.ocr_blocks)
        
        # 应该找到多个匹配的块
        self.assertGreater(len(matched_indices), 0)
        
        # 验证匹配的块包含相关文本
        matched_texts = [self.ocr_blocks[i]["words"] for i in matched_indices]
        self.assertIn("葡萄糖", matched_texts)


class TestRowCoordinateExtractor(unittest.TestCase):
    """测试行坐标提取器"""
    
    def setUp(self):
        """设置测试数据"""
        self.extractor = RowCoordinateExtractor(y_tolerance=10.0)
        
        # 模拟同一行的OCR块（Y坐标相近）
        self.ocr_blocks = [
            {
                "words": "葡萄糖",
                "location": [[100, 200], [150, 200], [150, 220], [100, 220]],
                "confidence": 0.95
            },
            {
                "words": "4.90",
                "location": [[200, 205], [230, 205], [230, 225], [200, 225]],
                "confidence": 0.98
            },
            {
                "words": "胆固醇",
                "location": [[100, 250], [150, 250], [150, 270], [100, 270]],
                "confidence": 0.93
            }
        ]
    
    def test_get_block_center_y(self):
        """测试计算文本块中心Y坐标"""
        location = [[100, 200], [150, 200], [150, 220], [100, 220]]
        center_y = self.extractor.get_block_center_y(location)
        self.assertEqual(center_y, 210.0)  # (200+200+220+220)/4 = 210
    
    def test_get_block_bounds(self):
        """测试计算文本块边界"""
        location = [[100, 200], [150, 200], [150, 220], [100, 220]]
        x_min, y_min, x_max, y_max = self.extractor.get_block_bounds(location)
        
        self.assertEqual(x_min, 100.0)
        self.assertEqual(y_min, 200.0)
        self.assertEqual(x_max, 150.0)
        self.assertEqual(y_max, 220.0)
    
    def test_group_blocks_by_row(self):
        """测试按行分组OCR块"""
        block_indices = [0, 1, 2]  # 前两个在同一行，第三个在不同行
        rows = self.extractor.group_blocks_by_row(block_indices, self.ocr_blocks)
        
        # 应该分成两行
        self.assertEqual(len(rows), 2)
        
        # 第一行应该包含前两个块
        self.assertEqual(len(rows[0]), 2)
        self.assertIn(0, rows[0])
        self.assertIn(1, rows[0])
        
        # 第二行应该包含第三个块
        self.assertEqual(len(rows[1]), 1)
        self.assertIn(2, rows[1])
    
    def test_calculate_row_coordinates(self):
        """测试计算行坐标"""
        row_block_indices = [0, 1]  # 同一行的两个块
        row_coord = self.extractor.calculate_row_coordinates(
            row_block_indices, self.ocr_blocks, 0, [0]
        )
        
        # 验证坐标计算
        self.assertEqual(row_coord.row_index, 0)
        self.assertEqual(row_coord.x_min, 100.0)  # 最小X
        self.assertEqual(row_coord.x_max, 230.0)  # 最大X
        self.assertEqual(row_coord.y_min, 200.0)  # 最小Y
        self.assertEqual(row_coord.y_max, 225.0)  # 最大Y
        self.assertEqual(len(row_coord.matched_blocks), 2)
        self.assertEqual(row_coord.test_item_indices, [0])


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置集成测试数据"""
        # 模拟完整的测试场景
        self.test_items = [
            TestItem(
                test_code="GLU",
                test_name="葡萄糖",
                test_value="4.90",
                test_flag=0,
                test_type="数值",
                test_unit="mmol/L",
                reference_value="3.90-6.10"
            ),
            TestItem(
                test_code="CHOL",
                test_name="胆固醇",
                test_value="5.2",
                test_flag=1,
                test_type="数值",
                test_unit="mmol/L",
                reference_value="3.0-5.0"
            )
        ]
        
        self.ocr_blocks = [
            {
                "words": "葡萄糖",
                "location": [[100, 200], [150, 200], [150, 220], [100, 220]],
                "confidence": 0.95
            },
            {
                "words": "4.90",
                "location": [[200, 205], [230, 205], [230, 225], [200, 225]],
                "confidence": 0.98
            },
            {
                "words": "mmol/L",
                "location": [[400, 200], [450, 200], [450, 220], [400, 220]],
                "confidence": 0.90
            },
            {
                "words": "胆固醇",
                "location": [[100, 250], [150, 250], [150, 270], [100, 270]],
                "confidence": 0.93
            },
            {
                "words": "5.2",
                "location": [[200, 255], [230, 255], [230, 275], [200, 275]],
                "confidence": 0.97
            }
        ]
    
    def test_extract_row_coordinates(self):
        """测试完整的行坐标提取流程"""
        row_coordinates = extract_row_coordinates(self.test_items, self.ocr_blocks)
        
        # 应该提取到两行
        self.assertEqual(len(row_coordinates), 2)
        
        # 验证行按Y坐标排序
        self.assertLess(row_coordinates[0].center_y, row_coordinates[1].center_y)
        
        # 验证每行都有关联的测试项目
        for row in row_coordinates:
            self.assertGreater(len(row.test_item_indices), 0)
    
    def test_coordinate_utilities(self):
        """测试坐标工具函数"""
        row_coordinates = extract_row_coordinates(self.test_items, self.ocr_blocks)
        
        # 测试Y坐标提取
        y_coords = get_row_y_coordinates(row_coordinates)
        self.assertEqual(len(y_coords), len(row_coordinates))
        
        # 测试边界框提取
        bounding_boxes = get_row_bounding_boxes(row_coordinates)
        self.assertEqual(len(bounding_boxes), len(row_coordinates))
        
        # 测试JSON导出
        json_data = export_coordinates_to_json(row_coordinates, self.test_items)
        self.assertEqual(json_data["total_rows"], len(row_coordinates))
        self.assertIn("rows", json_data)


def run_unit_tests():
    """运行单元测试"""
    print("🧪 运行单元测试...")
    print("=" * 50)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestCoordinateMapper))
    test_suite.addTest(unittest.makeSuite(TestRowCoordinateExtractor))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


def run_integration_tests():
    """运行集成测试"""
    print("\n🔗 运行集成测试...")
    print("=" * 50)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


def demo_coordinate_extraction():
    """演示坐标提取功能"""
    print("\n🎯 坐标提取功能演示...")
    print("=" * 50)
    
    # 创建测试数据
    test_items = [
        TestItem(
            test_code="GLU",
            test_name="葡萄糖",
            test_value="4.90",
            test_flag=0,
            test_type="数值",
            test_unit="mmol/L",
            reference_value="3.90-6.10"
        ),
        TestItem(
            test_code="CHOL",
            test_name="胆固醇",
            test_value="5.2",
            test_flag=1,
            test_type="数值",
            test_unit="mmol/L",
            reference_value="3.0-5.0"
        )
    ]
    
    ocr_blocks = [
        {
            "words": "葡萄糖",
            "location": [[100, 200], [150, 200], [150, 220], [100, 220]],
            "confidence": 0.95
        },
        {
            "words": "4.90",
            "location": [[200, 205], [230, 205], [230, 225], [200, 225]],
            "confidence": 0.98
        },
        {
            "words": "mmol/L",
            "location": [[400, 200], [450, 200], [450, 220], [400, 220]],
            "confidence": 0.90
        },
        {
            "words": "胆固醇",
            "location": [[100, 250], [150, 250], [150, 270], [100, 270]],
            "confidence": 0.93
        },
        {
            "words": "5.2",
            "location": [[200, 255], [230, 255], [230, 275], [200, 275]],
            "confidence": 0.97
        }
    ]
    
    print("📝 测试数据:")
    print(f"  测试项目数: {len(test_items)}")
    print(f"  OCR文本块数: {len(ocr_blocks)}")
    
    # 执行坐标提取
    print("\n🔍 执行坐标提取...")
    row_coordinates = extract_row_coordinates(test_items, ocr_blocks)
    
    print(f"✅ 提取完成，识别到 {len(row_coordinates)} 行")
    
    # 显示结果
    print_coordinate_summary(row_coordinates, test_items)
    
    # 显示Y坐标
    y_coords = get_row_y_coordinates(row_coordinates)
    print(f"\n📏 Y坐标范围:")
    for i, (y_min, y_max) in enumerate(y_coords):
        print(f"  行 {i}: {y_min:.1f} - {y_max:.1f}")
    
    # 导出JSON
    json_data = export_coordinates_to_json(row_coordinates, test_items)
    print(f"\n📄 JSON导出示例:")
    print(json.dumps(json_data, ensure_ascii=False, indent=2)[:500] + "...")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="坐标提取功能测试")
    parser.add_argument("--unit-tests", action="store_true", help="只运行单元测试")
    parser.add_argument("--integration-tests", action="store_true", help="只运行集成测试")
    parser.add_argument("--demo", action="store_true", help="运行功能演示")
    
    args = parser.parse_args()
    
    success = True
    
    if args.unit_tests or (not args.integration_tests and not args.demo):
        success &= run_unit_tests()
    
    if args.integration_tests or (not args.unit_tests and not args.demo):
        success &= run_integration_tests()
    
    if args.demo or (not args.unit_tests and not args.integration_tests):
        demo_coordinate_extraction()
    
    if not args.demo:
        if success:
            print("\n✅ 所有测试通过！")
        else:
            print("\n❌ 部分测试失败！")
            sys.exit(1)


if __name__ == "__main__":
    main()
