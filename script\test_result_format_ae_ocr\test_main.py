# -*- coding: utf-8 -*-
"""
医疗检验单识别系统测试程序

包含测试、评估和演示功能：
- 支持单个图片文件或文件夹批量处理
- 集成OCR识别功能
- 可选的答案对比评估
- 命令行参数支持
"""

import sys
import os
import json
import logging
import argparse
import time
import re
import threading
from pathlib import Path
from tabulate import tabulate
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录 (假设项目根目录是 d:\Code\smo-ai-backend)
# 向上两级目录，从 script/test_result_format_ae_ocr/test_main.py 到 d:\Code\smo-ai-backend
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入主功能模块
from script.test_result_format_ae_ocr.main import process_medical_ocr
from script.test_result_format_ae_ocr.evaluation import (
    load_ground_truth, 
    evaluate_ocr_results, 
    print_evaluation_report, 
    evaluate_ocr_results_with_cache
)
from script.test_result_format_ae_ocr.utils.ocr_client import OCRClient

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceTracker:
    """
    性能追踪器类，用于记录和统计处理性能
    """
    
    def __init__(self):
        self.lock = threading.Lock()
        self.session_start_time = None
        self.session_end_time = None
        self.file_records = []  # 存储每个文件的处理记录
        self.total_files = 0
        self.successful_files = 0
        self.failed_files = 0
        self.total_items = 0
        self.total_ocr_time = 0.0
        self.total_processing_time = 0.0
        self.total_time = 0.0
    
    def reset(self):
        """重置所有统计数据"""
        with self.lock:
            self.session_start_time = None
            self.session_end_time = None
            self.file_records.clear()
            self.total_files = 0
            self.successful_files = 0
            self.failed_files = 0
            self.total_items = 0
            self.total_ocr_time = 0.0
            self.total_processing_time = 0.0
            self.total_time = 0.0
    
    def start_session(self):
        """开始一个新的处理会话"""
        with self.lock:
            self.session_start_time = time.time()
            self.session_end_time = None
            self.file_records.clear()
            self.total_files = 0
            self.successful_files = 0
            self.failed_files = 0
            self.total_items = 0
            self.total_ocr_time = 0.0
            self.total_processing_time = 0.0
            self.total_time = 0.0
    
    def end_session(self):
        """结束当前处理会话"""
        with self.lock:
            self.session_end_time = time.time()
    
    def record_file_processing(self, filename: str, ocr_time: float, processing_time: float, 
                             total_time: float, item_count: int, success: bool):
        """记录单个文件的处理结果"""
        with self.lock:
            record = {
                'filename': filename,
                'ocr_time': ocr_time,
                'processing_time': processing_time,
                'total_time': total_time,
                'item_count': item_count,
                'success': success,
                'timestamp': datetime.now()
            }
            self.file_records.append(record)
            
            # 更新统计信息
            self.total_files += 1
            if success:
                self.successful_files += 1
                self.total_items += item_count
                self.total_ocr_time += ocr_time
                self.total_processing_time += processing_time
                self.total_time += total_time
            else:
                self.failed_files += 1
    
    def get_session_duration(self) -> float:
        """获取会话总时长"""
        if self.session_start_time is None:
            return 0.0
        end_time = self.session_end_time or time.time()
        return end_time - self.session_start_time
    
    def get_statistics(self) -> dict:
        """获取详细统计信息"""
        with self.lock:
            session_duration = self.get_session_duration()
            
            stats = {
                'session_duration': session_duration,
                'total_files': self.total_files,
                'successful_files': self.successful_files,
                'failed_files': self.failed_files,
                'success_rate': (self.successful_files / self.total_files * 100) if self.total_files > 0 else 0,
                'total_items': self.total_items,
                'total_ocr_time': self.total_ocr_time,
                'total_processing_time': self.total_processing_time,
                'total_time': self.total_time,
                'avg_ocr_time': (self.total_ocr_time / self.successful_files) if self.successful_files > 0 else 0,
                'avg_processing_time': (self.total_processing_time / self.successful_files) if self.successful_files > 0 else 0,
                'avg_total_time': (self.total_time / self.successful_files) if self.successful_files > 0 else 0,
                'avg_items_per_file': (self.total_items / self.successful_files) if self.successful_files > 0 else 0,
                'throughput_files_per_second': (self.successful_files / session_duration) if session_duration > 0 else 0,
                'throughput_items_per_second': (self.total_items / session_duration) if session_duration > 0 else 0
            }
            
            return stats
    
    def print_performance_report(self):
        """打印详细的性能报告"""
        stats = self.get_statistics()
        
        print("\n" + "=" * 60)
        print("📊 性能统计报告")
        print("=" * 60)
        
        # 基本统计
        basic_stats = [
            ["会话总时长", f"{stats['session_duration']:.2f} 秒"],
            ["处理文件总数", f"{stats['total_files']} 个"],
            ["成功处理文件", f"{stats['successful_files']} 个"],
            ["失败文件", f"{stats['failed_files']} 个"],
            ["成功率", f"{stats['success_rate']:.1f}%"],
            ["识别检验项目总数", f"{stats['total_items']} 个"]
        ]
        
        print("\n📈 基本统计:")
        print(tabulate(basic_stats, headers=["指标", "数值"], tablefmt="grid"))
        
        # 时间统计
        if stats['successful_files'] > 0:
            time_stats = [
                ["OCR总时间", f"{stats['total_ocr_time']:.2f} 秒"],
                ["医疗处理总时间", f"{stats['total_processing_time']:.2f} 秒"],
                ["文件处理总时间", f"{stats['total_time']:.2f} 秒"],
                ["平均OCR时间", f"{stats['avg_ocr_time']:.2f} 秒/文件"],
                ["平均医疗处理时间", f"{stats['avg_processing_time']:.2f} 秒/文件"],
                ["平均总处理时间", f"{stats['avg_total_time']:.2f} 秒/文件"]
            ]
            
            print("\n⏱️ 时间统计:")
            print(tabulate(time_stats, headers=["指标", "数值"], tablefmt="grid"))
        
        # 吞吐量统计
        throughput_stats = [
            ["文件处理速度", f"{stats['throughput_files_per_second']:.2f} 文件/秒"],
            ["检验项目处理速度", f"{stats['throughput_items_per_second']:.2f} 项目/秒"],
            ["平均每文件检验项目数", f"{stats['avg_items_per_file']:.1f} 个"]
        ]
        
        print("\n🚀 吞吐量统计:")
        print(tabulate(throughput_stats, headers=["指标", "数值"], tablefmt="grid"))
        
        print("=" * 60)

# 全局统计信息
global_stats = {}

# 创建全局性能追踪器实例
performance_tracker = PerformanceTracker()


def process_single_image(image_path: str, ocr_client: OCRClient = None, performance_tracker: PerformanceTracker = None) -> tuple:
    """
    处理单张图片：OCR识别 + 医疗检验单处理
    
    Args:
        image_path: 图片文件路径
        ocr_client: OCR客户端实例
        performance_tracker: 性能追踪器实例，可选
        
    Returns:
        tuple: (ocr_text, processed_result)
    """
    if ocr_client is None:
        ocr_client = OCRClient()
    
    filename = Path(image_path).name
    total_start_time = time.time()
    
    logger.info(f"开始处理图片: {image_path}")
    
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        # 检查文件格式
        if not ocr_client.is_supported_image_format(image_path):
            raise ValueError(f"不支持的图片格式: {image_path}")
        
        # OCR识别
        logger.info("正在进行OCR识别...")
        ocr_start_time = time.time()
        ocr_text = ocr_client.ocr_main(image_path)
        ocr_end_time = time.time()
        ocr_time = ocr_end_time - ocr_start_time
        
        if not ocr_text.strip():
            logger.warning(f"OCR识别结果为空: {image_path}")
            if performance_tracker:
                performance_tracker.record_file_processing(filename, ocr_time, 0, time.time() - total_start_time, 0, False)
            return ocr_text, []
        
        logger.info(f"OCR识别完成，文本长度: {len(ocr_text)}，耗时: {ocr_time:.2f}秒")
        
        # 医疗检验单处理
        logger.info("正在进行医疗检验单处理...")
        processing_start_time = time.time()
        processed_result = process_medical_ocr(ocr_text)
        processing_end_time = time.time()
        processing_time = processing_end_time - processing_start_time
        
        total_time = time.time() - total_start_time
        item_count = len(processed_result) if processed_result else 0
        
        logger.info(f"处理完成，识别到 {item_count} 个检验项目，医疗处理耗时: {processing_time:.2f}秒")
        
        # 记录性能数据
        if performance_tracker:
            performance_tracker.record_file_processing(filename, ocr_time, processing_time, total_time, item_count, True)
        
        return ocr_text, processed_result

    except Exception as e:
        total_time = time.time() - total_start_time
        logger.error(f"处理文件 {filename} 时发生错误: {e}")
        if performance_tracker:
            performance_tracker.record_file_processing(filename, 0, 0, total_time, 0, False)
        raise



def process_image_directory(directory_path: str, ocr_client: OCRClient = None, max_workers: int = 5, performance_tracker: PerformanceTracker = None) -> dict:
    """
    批量处理文件夹中的图片（支持并发处理）
    
    Args:
        directory_path: 图片文件夹路径
        ocr_client: OCR客户端实例
        max_workers: 最大并发线程数，默认5
        performance_tracker: 性能追踪器实例，可选

    Returns:
        dict: {filename: (ocr_text, processed_result)}
    """
    if ocr_client is None:
        ocr_client = OCRClient()
    
    directory = Path(directory_path)
    if not directory.exists():
        raise FileNotFoundError(f"文件夹不存在: {directory_path}")
    
    if not directory.is_dir():
        raise ValueError(f"路径不是文件夹: {directory_path}")
    
    results = {}
    supported_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.webp')
    
    # 获取所有支持的图片文件（避免重复）
    image_files = set()  # 使用set避免重复
    for ext in supported_extensions:
        image_files.update(directory.glob(f"*{ext}"))
        image_files.update(directory.glob(f"*{ext.upper()}"))
    
    image_files = list(image_files)  # 转换回list
    
    if not image_files:
        logger.warning(f"文件夹中没有找到支持的图片文件: {directory_path}")
        return results
    
    logger.info(f"找到 {len(image_files)} 个图片文件，将使用 {max_workers} 个线程并发处理")
    
    # 自定义排序函数：优先按数字排序，然后按字符排序
    def natural_sort_key(file_path):
        import re
        # 提取文件名（不含扩展名）
        name = file_path.stem
        # 将文件名分解为数字和非数字部分
        parts = re.split(r'(\d+)', name)
        # 将数字部分转换为整数，非数字部分保持字符串
        result = []
        for part in parts:
            if part.isdigit():
                result.append(int(part))
            else:
                result.append(part)
        return result
    
    # 按自然排序对文件进行排序
    sorted_image_files = sorted(image_files, key=natural_sort_key)
    
    # 定义单个文件处理函数（用于线程池）
    def process_single_file_wrapper(args):
        image_file, file_index, total_files = args
        try:
            logger.info(f"[线程] 开始处理第 {file_index}/{total_files} 个文件: {image_file.name}")
            
            # 为每个线程创建独立的OCR客户端实例，避免线程冲突
            thread_ocr_client = OCRClient()
            ocr_text, processed_result = process_single_image(str(image_file), thread_ocr_client, performance_tracker)
            
            logger.info(f"[线程] 完成处理第 {file_index}/{total_files} 个文件: {image_file.name}")
            return image_file.stem, (ocr_text, processed_result)
        except Exception as e:
            logger.error(f"[线程] 处理文件 {image_file.name} 时发生错误: {e}")
            return image_file.stem, ("", [])
    
    # 准备任务参数
    tasks = [(image_file, i + 1, len(sorted_image_files)) for i, image_file in enumerate(sorted_image_files)]
    
    # 使用线程池并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务并记录任务顺序
        future_to_index = {}
        for i, task in enumerate(tasks):
            future = executor.submit(process_single_file_wrapper, task)
            future_to_index[future] = i
        
        # 收集结果（保持并行处理）
        completed_count = 0
        results_by_index = {}  # 用索引来保持顺序
        
        # 并行等待所有任务完成
        for future in as_completed(future_to_index):
            task_index = future_to_index[future]
            image_file = sorted_image_files[task_index]
            try:
                filename, result = future.result()
                results_by_index[task_index] = (filename, result)
                completed_count += 1
                logger.info(f"进度: {completed_count}/{len(sorted_image_files)} 个文件处理完成")
            except Exception as e:
                logger.error(f"处理文件 {image_file.name} 时发生异常: {e}")
                results_by_index[task_index] = (image_file.stem, ("", []))
                completed_count += 1
        
        # 按照原始排序顺序构建结果字典
        for i in range(len(sorted_image_files)):
            if i in results_by_index:
                filename, result = results_by_index[i]
                results[filename] = result
    
        logger.info(f"所有文件处理完成！成功处理 {len([r for r in results.values() if r[0]])} 个文件")
    return results


def process_medical_ocr_with_debug(ocr_text: str):
    """
    处理医疗检验单OCR文本（带调试输出）
    
    Args:
        ocr_text: OCR识别的文本

    Returns:
        ProcessingResult: 处理结果对象
    """
    # 调用核心处理函数
    result = process_medical_ocr(ocr_text)
    
    # 添加调试输出
    if result:
        print("\n检验项目识别结果:")
        headers = ["检查代码", "检查名称", "检查结果值", "异常符号", "检查单位", "参考范围"]
        table_data = []
        for item in result:
            table_data.append([
                item.test_code or "",
                item.test_name or "",
                item.test_value or "",
                item.abnormal_symbol or "",
                item.test_unit or "",
                item.reference_value or ""
            ])
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
    
    return result

# 默认配置
DEFAULT_CONFIG = {
    'mode': 'eval',  # 默认运行模式: test, eval, demo
    'input': r"D:\data\ocr\13张图片",  # 默认输入文件或文件夹
    # 'input': r"D:\下载\高匹配成功率汇总\img",  # 默认输入文件或文件夹

    # 'ground_truth': r"D:\下载\高匹配成功率汇总\按图片分表格.xlsx",  # 默认正确答案文件（仅在eval和demo模式下使用）
    # 'ground_truth': r"D:\data\ocr\13张图片\答案.xlsx",  # 默认正确答案文件（仅在eval和demo模式下使用）
    'max_workers': 13,  # 并发线程数，默认5
}

def get_default_config():
    """获取默认配置"""
    return DEFAULT_CONFIG.copy()

def update_default_config(**kwargs):
    """更新默认配置"""
    DEFAULT_CONFIG.update(kwargs)


def match_results_with_ground_truth(results: dict, ground_truth_data: dict) -> tuple:
    """
    根据文件名匹配处理结果和正确答案
    
    Args:
        results: 处理结果字典 {filename: (ocr_text, processed_result)}
        ground_truth_data: 正确答案数据字典 {sheet_name: data}
        
    Returns:
        tuple: (matched_ocr_texts, matched_ground_truth, file_mapping)
    """
    matched_ocr_texts = []
    matched_ground_truth = []
    file_mapping = []  # 记录文件名和匹配的Sheet的对应关系

    # 自定义排序函数：优先按数字排序，然后按字符排序
    def natural_sort_key(filename):
        import re
        # 将文件名分解为数字和非数字部分
        parts = re.split(r'(\d+)', filename)
        # 将数字部分转换为整数，非数字部分保持字符串
        result = []
        for part in parts:
            if part.isdigit():
                result.append(int(part))
            else:
                result.append(part)
        return result
    
    ground_truth_sheet_names = list(ground_truth_data.keys())
    ground_truth_sheet_values = list(ground_truth_data.values())

    # 按自然排序处理文件
    for filename in sorted(results.keys(), key=natural_sort_key):
        ocr_text, _ = results[filename]
        # 尝试匹配文件名和Sheet名称
        matched = False
        
        # 方式1: 直接匹配文件名和Sheet名称 (大小写不敏感)
        for i, sheet_name in enumerate(ground_truth_sheet_names):
            if filename.lower() == str(sheet_name).lower():
                matched_ocr_texts.append(ocr_text)
                matched_ground_truth.append(ground_truth_sheet_values[i])
                file_mapping.append((filename, sheet_name, i))
                matched = True
                logger.info(f"文件 {filename} 匹配到Sheet '{sheet_name}'")
                break
        
        # 方式2: 尝试从文件名中提取数字匹配 (作为后备)
        if not matched:
            import re
            numbers = re.findall(r'\d+', filename)
            if numbers:
                try:
                    file_number = int(numbers[0])
                    # 假设 'Sheet1' 对应 file_number 1
                    if 1 <= file_number <= len(ground_truth_data):
                        sheet_index = file_number - 1
                        sheet_name = ground_truth_sheet_names[sheet_index]
                        sheet_data = ground_truth_sheet_values[sheet_index]

                        matched_ocr_texts.append(ocr_text)
                        matched_ground_truth.append(sheet_data)
                        file_mapping.append((filename, sheet_name, sheet_index))
                        matched = True
                        logger.info(f"文件 {filename} 通过数字 {file_number} 匹配到Sheet '{sheet_name}'")
                except (ValueError, IndexError):
                    pass
        
        if not matched:
            logger.warning(f"文件 {filename} 无法匹配到正确答案")
    
    return matched_ocr_texts, matched_ground_truth, file_mapping


def print_file_accuracy_report(evaluation_report: dict, file_mapping: list):
    """
    打印每个文件的正确率报告
    
    Args:
        evaluation_report: 评估报告字典
        file_mapping: 文件映射列表 [(filename, sheet_name, sheet_index), ...]
    """
    print("\n" + "=" * 80)
    print("📊 各文件正确率统计")
    print("=" * 80)
    
    if not file_mapping:
        print("❌ 没有匹配的文件")
        return
    
    # 准备表格数据
    headers = ["文件名", "匹配Sheet", "项目准确率", "字段准确率", "异常符号召回率", "异常符号精确率", "匹配项目数", "总项目数"]
    table_data = []
    
    evaluation_logs = evaluation_report.get('evaluation_logs', [])
    
    for i, (filename, sheet_name, sheet_index) in enumerate(file_mapping):
        if i < len(evaluation_logs):
            log = evaluation_logs[i]
            
            # 计算项目准确率
            item_accuracy = log['matched_item_count'] / log['ground_truth_item_count'] if log['ground_truth_item_count'] > 0 else 0.0
            
            # 字段准确率已经在log中
            field_accuracy = log.get('field_accuracy', 0.0)
            
            # 计算异常符号召回率和精确率
            abnormal_stats = log.get('abnormal_symbol_stats', {})
            abnormal_recall = 0.0
            abnormal_precision = 0.0
            
            if abnormal_stats:
                # 计算召回率
                if abnormal_stats.get('ground_truth_count', 0) > 0:
                    abnormal_recall = (abnormal_stats.get('ground_truth_count', 0) - 
                                     abnormal_stats.get('missed_count', 0)) / abnormal_stats.get('ground_truth_count', 0)
                
                # 计算精确率
                if abnormal_stats.get('predicted_count', 0) > 0:
                    abnormal_precision = (abnormal_stats.get('predicted_count', 0) - 
                                        abnormal_stats.get('false_positive_count', 0)) / abnormal_stats.get('predicted_count', 0)
            
            # 计算正确字段数和总字段数
            matched_items = log['matched_item_count']
            total_items = log['ground_truth_item_count']
            
            table_data.append([
                filename[:20] + "..." if len(filename) > 20 else filename,
                sheet_name,
                f"{item_accuracy:.1%}",
                f"{field_accuracy:.1%}",
                f"{abnormal_recall:.1%}" if abnormal_stats else "N/A",
                f"{abnormal_precision:.1%}" if abnormal_stats else "N/A",
                f"{matched_items}",
                f"{total_items}"
            ])
        else:
            # 如果没有对应的评估日志，显示错误
            table_data.append([
                filename[:20] + "..." if len(filename) > 20 else filename,
                sheet_name,
                "N/A",
                "N/A",
                "N/A",
                "N/A",
                "N/A",
                "N/A"
            ])
    
    print(tabulate(table_data, headers=headers, tablefmt="grid"))
    
    # 计算总体统计
    if evaluation_logs:
        total_item_accuracy = sum(log['matched_item_count'] / log['ground_truth_item_count'] 
                                if log['ground_truth_item_count'] > 0 else 0.0 
                                for log in evaluation_logs) / len(evaluation_logs)
        
        total_field_accuracy = sum(log.get('field_accuracy', 0.0) for log in evaluation_logs) / len(evaluation_logs)
        
        # 计算异常符号的总体统计
        total_abnormal_ground_truth = sum(log.get('abnormal_symbol_stats', {}).get('ground_truth_count', 0) for log in evaluation_logs)
        total_abnormal_predicted = sum(log.get('abnormal_symbol_stats', {}).get('predicted_count', 0) for log in evaluation_logs)
        total_abnormal_missed = sum(log.get('abnormal_symbol_stats', {}).get('missed_count', 0) for log in evaluation_logs)
        total_abnormal_false_positive = sum(log.get('abnormal_symbol_stats', {}).get('false_positive_count', 0) for log in evaluation_logs)
        
        # 计算总体异常符号召回率和精确率
        overall_abnormal_recall = 0.0
        if total_abnormal_ground_truth > 0:
            overall_abnormal_recall = (total_abnormal_ground_truth - total_abnormal_missed) / total_abnormal_ground_truth
        
        overall_abnormal_precision = 0.0
        if total_abnormal_predicted > 0:
            overall_abnormal_precision = (total_abnormal_predicted - total_abnormal_false_positive) / total_abnormal_predicted
        
        # 计算F1分数
        overall_abnormal_f1 = 0.0
        if overall_abnormal_recall + overall_abnormal_precision > 0:
            overall_abnormal_f1 = 2 * (overall_abnormal_recall * overall_abnormal_precision) / (overall_abnormal_recall + overall_abnormal_precision)
        
        print(f"\n📈 总体平均正确率:")
        print(f"├─ 平均项目准确率: {total_item_accuracy:.1%}")
        print(f"└─ 平均字段准确率: {total_field_accuracy:.1%}")
        
        print(f"\n⚠️ 异常符号综合统计:")
        print(f"├─ 总异常符号数 (答案): {total_abnormal_ground_truth}")
        print(f"├─ 总异常符号数 (识别): {total_abnormal_predicted}")
        print(f"├─ 漏检数量: {total_abnormal_missed}")
        print(f"├─ 误检数量: {total_abnormal_false_positive}")
        print(f"├─ 总体召回率: {overall_abnormal_recall:.1%}")
        print(f"├─ 总体精确率: {overall_abnormal_precision:.1%}")
        # print(f"└─ 总体F1分数: {overall_abnormal_f1:.1%}")
    
    print("=" * 80)


def run_evaluation_mode(args):
    """运行评估模式"""
    logger.info("=" * 60)
    logger.info("运行评估模式")
    logger.info("=" * 60)
    
    # 创建性能追踪器
    performance_tracker = PerformanceTracker()
    performance_tracker.reset()
    performance_tracker.start_session()
    
    # 检查正确答案文件
    if not os.path.exists(args.ground_truth):
        raise FileNotFoundError(f"正确答案文件不存在: {args.ground_truth}")
    
    # 加载正确答案，同时获取sheet名称
    logger.info(f"加载正确答案: {args.ground_truth}")
    import pandas as pd
    try:
        ground_truth_excel = pd.read_excel(args.ground_truth, sheet_name=None)
        ground_truth_sheets = {name: data for name, data in ground_truth_excel.items()}
        logger.info(f"加载了 {len(ground_truth_sheets)} 个正确答案 (sheets): {list(ground_truth_sheets.keys())}")
    except Exception as e:
        logger.error(f"加载或解析Excel文件失败: {e}")
        # 如果新方法失败，回退到旧方法
        logger.info("回退到原始的答案加载方式...")
        ground_truth_data = load_ground_truth(args.ground_truth)
        ground_truth_sheets = {f"Sheet{i+1}": data for i, data in enumerate(ground_truth_data)}
        logger.info(f"加载了 {len(ground_truth_sheets)} 个正确答案")

    # 创建OCR客户端
    ocr_client = OCRClient()
    
    # 处理输入
    if os.path.isfile(args.input):
        # 单个文件
        logger.info("处理单个图片文件")
        filename = Path(args.input).stem
        ocr_text, processed_result = process_single_image(args.input, ocr_client, performance_tracker)
        results = {filename: (ocr_text, processed_result)}
    else:
        # 文件夹
        logger.info("批量处理文件夹中的图片")
        max_workers = getattr(args, 'max_workers', 5)  # 从args获取并发数，默认5
        results = process_image_directory(args.input, ocr_client, max_workers, performance_tracker)
    
    # 匹配结果和正确答案
    matched_ocr_texts, matched_ground_truth, file_mapping = match_results_with_ground_truth(results, ground_truth_sheets)
    
    if not matched_ocr_texts:
        logger.error("没有找到匹配的测试用例和正确答案")
        return
    
    logger.info(f"匹配到 {len(matched_ocr_texts)} 个测试用例")
    
    # 执行评估
    logger.info("开始执行评估...")
    # 从results中提取已处理的结果，避免重复调用大模型
    matched_processed_results = []
    for filename in sorted(results.keys(), key=lambda x: [int(c) if c.isdigit() else c for c in re.split(r'(\d+)', x)]):
        if filename in [mapping[0] for mapping in file_mapping]:
            _, processed_result = results[filename]
            matched_processed_results.append(processed_result)
    
    evaluation_report = evaluate_ocr_results_with_cache(matched_ocr_texts, matched_ground_truth, matched_processed_results)
    
    # 打印评估报告
    print_evaluation_report(evaluation_report)
    
    # 打印每个文件的正确率
    print_file_accuracy_report(evaluation_report, file_mapping)
    
    # 结束性能追踪并输出报告
    performance_tracker.end_session()
    performance_tracker.print_performance_report()


def run_test_mode(args):
    """运行测试模式（不进行答案对比）"""
    logger.info("=" * 60)
    logger.info("运行测试模式")
    logger.info("=" * 60)
    
    # 创建性能追踪器
    performance_tracker = PerformanceTracker()
    performance_tracker.reset()
    performance_tracker.start_session()
    
    # 创建OCR客户端
    ocr_client = OCRClient()
    
    # 处理输入
    if os.path.isfile(args.input):
        # 单个文件
        logger.info(f"处理单个图片文件: {args.input}")
        ocr_text, processed_result = process_single_image(args.input, ocr_client, performance_tracker)
        
        print("\n" + "=" * 50)
        print(f"文件: {Path(args.input).name}")
        print("=" * 50)
        print("\n📄 OCR识别结果:")
        print("-" * 30)
        print(ocr_text)
        
        print("\n🔍 检验项目识别结果:")
        print("-" * 30)
        if processed_result:
            headers = ["检查代码", "检查名称", "检查结果值", "异常符号", "检查单位", "参考范围"]
            table_data = []
            for item in processed_result:
                table_data.append([
                    item.test_code or "",
                    item.test_name or "",
                    item.test_value or "",
                    item.abnormal_symbol or "",
                    item.test_unit or "",
                    item.reference_value or ""
                ])
            print(tabulate(table_data, headers=headers, tablefmt="grid"))
            print(f"\n✅ 共识别到 {len(processed_result)} 个检验项目")
        else:
            print("❌ 未识别到任何检验项目")
    
    else:
        # 文件夹
        logger.info(f"批量处理文件夹: {args.input}")
        max_workers = getattr(args, 'max_workers', 5)  # 从args获取并发数，默认5
        results = process_image_directory(args.input, ocr_client, max_workers, performance_tracker)
        
        print("\n" + "=" * 60)
        print("批量处理结果汇总")
        print("=" * 60)
        
        total_items = 0
        for filename, (ocr_text, processed_result) in results.items():
            print(f"\n📁 文件: {filename}")
            print("-" * 40)
            
            if processed_result:
                print(f"✅ 识别到 {len(processed_result)} 个检验项目")
                total_items += len(processed_result)
                
                # 显示前几个项目作为示例
                headers = ["检查代码", "检查名称", "检查结果值", "异常符号"]
                table_data = []
                display_count = min(3, len(processed_result))  # 最多显示3个
                
                for item in processed_result[:display_count]:
                    table_data.append([
                        item.test_code or "",
                        item.test_name or "",
                        item.test_value or "",
                        item.abnormal_symbol or ""
                    ])
                
                if table_data:
                    print(tabulate(table_data, headers=headers, tablefmt="grid"))
                    if len(processed_result) > display_count:
                        print(f"... 还有 {len(processed_result) - display_count} 个项目")
            else:
                print("❌ 未识别到任何检验项目")
        
        print(f"\n🎉 批量处理完成！")
        print(f"📊 处理文件数: {len(results)}")
        print(f"📋 总检验项目数: {total_items}")
    
    # 结束性能追踪并输出报告
    performance_tracker.end_session()
    performance_tracker.print_performance_report()


def run_demo_mode(args):
    """运行演示模式（原有功能）"""
    logger.info("=" * 60)
    logger.info("运行演示模式")
    logger.info("=" * 60)
    
    # 创建性能追踪器
    performance_tracker = PerformanceTracker()
    performance_tracker.reset()
    performance_tracker.start_session()
    
    # 测试用例OCR文本
    ocr_test_cases = [
        """
    第1页/共1页 尿液常规分析（尿10项/有形成分/镜检） 周栋强 采样时间：2024-09-1406:38\n北京大学人民医院检验报告单\nPeking University People's Hospital Laboratory Report 托：\n姓名： 周栋强 卡号/病案号： 4495893 标本编号： 124091401022 科别：血液科病房 床号： 027\n性别：男 年龄： 47岁 流水号： 2178 标本种类： 尿 病房：18A病区 申请医生：陈育红\n检验项目： 尿液常规分析（尿10项/有形 执行科室： 检验科病房临检 临床诊断： 异基因造血干细胞移植术\n检验项目 结果 单位 参考区间 检验项目 结果 单位 参考区间\nSG *（干化学）比重 6101 1.003-1.030 SPERM（尿流式）精子 0 /ul 0-0\nGLU *（干化学）葡萄糖 阴性 阴性 SRC （尿流式）小圆上皮细胞 0.4 /ul 0-3\nPRO *（干化学）蛋白 + 阴性 EC （尿流式)上皮细胞 0 /uL 0-5\nPH *（干化学）酸碱度 7.5 4.5-8.0 M
        """,
    ]

    try:
        # 加载正确答案
        ground_truth_data = load_ground_truth(args.ground_truth)
        
        # 检查测试用例和正确答案数量匹配
        if len(ground_truth_data) != len(ocr_test_cases):
            logger.warning(f"正确答案Sheet数量 ({len(ground_truth_data)}) 与测试用例数量 ({len(ocr_test_cases)}) 不匹配")
            min_cases = min(len(ground_truth_data), len(ocr_test_cases))
            ocr_test_cases = ocr_test_cases[:min_cases]
            ground_truth_data = ground_truth_data[:min_cases]
            logger.info(f"将评测前 {min_cases} 个用例")

        # 执行评测
        print("\n开始执行评测...")
        evaluation_report = evaluate_ocr_results(ocr_test_cases, ground_truth_data, process_medical_ocr_with_debug)
        print_evaluation_report(evaluation_report)
        # 打印简化的评估报告
        print("\n" + "="*80)
        print("📊 评估结果总结")
        print("="*80)
        overall = evaluation_report['overall_metrics']
        print(f"总测试用例数: {evaluation_report['total_test_cases']}")
        print(f"项目匹配准确率: {overall['item_accuracy']}")
        print(f"字段准确率: {overall['field_accuracy']}")
        print(f"总错误数: {evaluation_report['error_summary']['total_errors']}")
        print("="*80)
        
        # 结束性能追踪并输出报告
        performance_tracker.end_session()
        performance_tracker.print_performance_report()

    except Exception as e:
        logger.critical(f"评测过程中发生严重错误: {e}")
        raise


def run_with_default_config():
    """使用默认配置运行"""
    config = get_default_config()
    
    print("医疗检验单OCR处理系统")
    print("=" * 50)
    print("🔧 使用默认配置运行")
    print(f"📁 模式: {config['mode']}")
    print(f"📄 输入: {config['input']}")
    if config['mode'] in ['eval', 'demo']:
        print(f"📊 答案文件: {config.get('ground_truth', '未配置')}")
    print("=" * 50)
    
    # 创建模拟的args对象
    class Args:
        def __init__(self, config):
            self.mode = config['mode']
            self.input = config['input']
            self.max_workers = config.get('max_workers', 5)
            # 只在需要的模式下设置 ground_truth
            if config['mode'] in ['eval', 'demo']:
                self.ground_truth = config.get('ground_truth', '')
            else:
                self.ground_truth = None
    
    args = Args(config)
    
    try:
        if args.mode == 'test':
            run_test_mode(args)
        elif args.mode == 'eval':
            run_evaluation_mode(args)
        elif args.mode == 'demo':
            run_demo_mode(args)
        else:
            print(f"❌ 不支持的模式: {args.mode}")
            
    except Exception as e:
        logger.critical(f"程序执行过程中发生严重错误: {e}")
        raise


def main():
    """主程序 - 医疗检验单OCR处理系统测试入口"""
    # 如果没有命令行参数，使用默认配置
    if len(sys.argv) == 1:
        print("📝 未提供命令行参数，使用默认配置运行...")
        print("💡 提示: 可以修改代码中的 DEFAULT_CONFIG 来调整默认配置")
        print("💡 或者使用命令行参数: python test_main.py test <文件路径>")
        print()
        run_with_default_config()
        return
    
    parser = argparse.ArgumentParser(
        description="医疗检验单OCR处理系统测试工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 直接运行（使用默认配置）
  python test_main.py
  
  # 测试模式 - 处理单个图片文件
  python test_main.py test D:/data/ocr/13张图片/13tu/1尿液常规分析.png
  
  # 测试模式 - 批量处理文件夹
  python test_main.py test D:\\data\\ocr\\13张图片\\13tu
  
  # 测试模式 - 批量处理文件夹（指定10个并发线程）
  python test_main.py test D:\\data\\ocr\\13张图片\\13tu --max-workers 10
  
  # 评估模式 - 单个文件与答案对比
  python test_main.py eval image.jpg --ground-truth answers.xlsx
  
  # 评估模式 - 批量处理与答案对比（指定8个并发线程）
  python test_main.py eval /path/to/images/ --ground-truth answers.xlsx --max-workers 8
  
默认配置:
  - 模式: eval
  - 输入: D:\\下载\\高匹配成功率汇总\\img
  - 答案文件: D:\\下载\\高匹配成功率汇总\\按图片分表格.xlsx
  
文件名匹配规则:
  - 图片文件名应与Excel答案Sheet名称对应
  - 例如: Sheet1.jpg 对应 Excel中的Sheet1
  - 或者: 1.jpg 对应 Excel中的Sheet1
        """
    )
    
    # 添加子命令
    subparsers = parser.add_subparsers(dest='mode', help='运行模式', required=True)
    
    # 测试模式
    test_parser = subparsers.add_parser('test', help='测试模式（不进行答案对比）')
    test_parser.add_argument('input', help='输入图片文件或文件夹路径')
    test_parser.add_argument('--max-workers', '-w', type=int, default=5,
                           help='并发线程数（默认5）')
    
    # 评估模式
    eval_parser = subparsers.add_parser('eval', help='评估模式（与正确答案对比）')
    eval_parser.add_argument('input', help='输入图片文件或文件夹路径')
    eval_parser.add_argument('--ground-truth', '-g', required=True, 
                           help='正确答案Excel文件路径')
    eval_parser.add_argument('--max-workers', '-w', type=int, default=5,
                           help='并发线程数（默认5）')
    
    # 演示模式（保持原有功能）
    demo_parser = subparsers.add_parser('demo', help='演示模式（使用内置测试用例）')
    demo_parser.add_argument('--ground-truth', '-g', 
                           default=r"D:\data\ocr\13张图片\答案.xlsx",
                           help='正确答案Excel文件路径')
    demo_parser.add_argument('--max-workers', '-w', type=int, default=5,
                           help='并发线程数（默认5）')
    
    args = parser.parse_args()
    
    if not args.mode:
        parser.print_help()
        return
    
    print("医疗检验单OCR处理系统测试工具")
    print("=" * 50)
    
    try:
        if args.mode == 'test':
            run_test_mode(args)
        elif args.mode == 'eval':
            run_evaluation_mode(args)
        elif args.mode == 'demo':
            run_demo_mode(args)
        else:
            parser.print_help()
            
    except Exception as e:
        logger.critical(f"程序执行过程中发生严重错误: {e}")
        raise


if __name__ == "__main__":
    main()