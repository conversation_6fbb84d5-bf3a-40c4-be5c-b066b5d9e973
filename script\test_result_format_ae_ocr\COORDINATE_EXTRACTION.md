# 坐标提取功能文档（重构版）

## 概述

坐标提取功能是对现有医疗检验单识别系统的增强，能够从结构化的检验报告中提取每行的坐标信息。该功能已经过重构优化，支持新的OCR数据结构，使用更准确的行分组逻辑，并提供标准化的坐标格式。

## 功能特性

### 🎯 核心功能
- **行级坐标提取**：提取整行检验项目的坐标信息，而非单个字段的精确位置
- **智能文本匹配**：使用模糊匹配算法将结构化数据映射回原始OCR坐标
- **边界框计算**：为每行计算完整的边界框（bounding box）信息
- **多页支持**：支持多页文档的坐标提取

### 📊 输出信息
- **Y坐标范围**：每行的最小和最大Y坐标
- **完整边界框**：每行的 (x_min, y_min, x_max, y_max) 坐标
- **行高和宽度**：计算得出的行尺寸信息
- **置信度**：基于OCR块置信度的行匹配置信度
- **关联信息**：每行关联的测试项目索引

## 架构设计

### 🏗️ 核心组件

1. **CoordinateMapper**：坐标映射器
   - 负责将结构化测试项目映射到OCR文本块
   - 使用文本相似度算法进行智能匹配
   - 支持可配置的相似度阈值

2. **RowCoordinateExtractor**：行坐标提取器
   - 将OCR文本块按Y坐标分组为行
   - 计算每行的边界框和统计信息
   - 支持可配置的Y坐标容差

3. **RowCoordinate**：行坐标数据模型
   - 存储完整的行坐标和元数据信息
   - 包含关联的OCR块和测试项目信息

4. **CoordinateExtractionNode**：PocketFlow节点
   - 集成到现有处理流程中
   - 在数据验证后执行坐标提取
   - 将坐标信息添加到TestItem对象中

### 🔄 工作流程

```
OCR识别 → 文本结构化 → 数据验证 → 坐标提取 → 结果输出
    ↓           ↓           ↓          ↓         ↓
文本+坐标   结构化数据   验证数据   坐标映射   增强结果
```

## 使用方法

### 📝 基本使用（重构后）

```python
from script.test_result_format_ae_ocr.main import process_medical_ocr
from script.test_result_format_ae_ocr.utils.ocr_client import OCRClient

# 创建OCR客户端
ocr_client = OCRClient()

# 获取OCR数据（包含坐标信息）
ocr_data = ocr_client.ocr_main_with_coordinates("test_image.jpg")

# 处理医疗检验单（自动启用坐标提取）
test_items = process_medical_ocr(
    ocr_data["text"],
    ocr_blocks=ocr_data["ocr_blocks"]  # 传入OCR块启用坐标提取
)

# 每个测试项目现在包含坐标信息
for item in test_items:
    if item.row_coordinates:
        print(f"{item.test_name}: 坐标点 {item.row_coordinates}")
```

### 🔧 高级使用（重构后）

```python
from script.test_result_format_ae_ocr.utils.coordinate_extraction import (
    extract_row_coordinates,
    get_row_y_coordinates,
    get_row_bounding_boxes,
    export_coordinates_to_json
)

# 新的OCR数据格式：[{"page": int, "words_block_list": [...]}]
ocr_data = [
    {
        "page": 1,
        "words_block_list": [
            {
                "words": "葡萄糖",
                "location": [[100, 200], [150, 200], [150, 220], [100, 220]],
                "confidence": 0.95,
                "line_break": True  # 行结束标记
            }
            # ... 更多OCR块
        ]
    }
]

# 直接使用坐标提取函数
row_coordinates = extract_row_coordinates(test_items, ocr_data)

# 获取Y坐标范围
y_coordinates = get_row_y_coordinates(row_coordinates)

# 获取完整边界框
bounding_boxes = get_row_bounding_boxes(row_coordinates)

# 导出为JSON格式
json_data = export_coordinates_to_json(row_coordinates, test_items)
```

### 🎮 演示脚本

```bash
# 使用实际图片演示
python demo_coordinate_extraction.py path/to/test_image.jpg

# 使用内置测试数据演示
python demo_coordinate_extraction.py --demo
```

## 数据结构

### 📦 RowCoordinate 对象

```python
@dataclass
class RowCoordinate:
    row_index: int                          # 行索引（从0开始）
    y_min: float                           # 行的最小Y坐标
    y_max: float                           # 行的最大Y坐标
    x_min: float                           # 行的最小X坐标
    x_max: float                           # 行的最大X坐标
    center_y: float                        # 行的中心Y坐标
    height: float                          # 行高度
    width: float                           # 行宽度
    matched_blocks: List[Dict[str, Any]]   # 匹配到的OCR文本块
    confidence: float                      # 匹配置信度
    test_item_indices: List[int]           # 关联的测试项目索引
```

### 🔬 增强的 TestItem 对象（重构后）

```python
@dataclass
class TestItem:
    # ... 原有字段 ...
    row_coordinates: Optional[List[List[float]]] = None   # 四个坐标点：[[左上x,左上y], [右上x,右上y], [右下x,右下y], [左下x,左下y]]
```

## 配置参数

### ⚙️ 可调参数（重构后）

- **similarity_threshold** (默认: 0.6)
  - 文本匹配相似度阈值
  - 范围: 0.0-1.0
  - 较高值要求更精确匹配，较低值允许更模糊匹配

- **line_break标记**
  - 使用OCR数据中的 `line_break: true` 标记来确定行边界
  - 不再依赖Y坐标容差，提高行识别准确性
  - 当OCR块包含 `line_break: true` 时，表示该块是当前行的最后一个元素

## 测试和验证

### 🧪 运行测试

```bash
# 运行所有测试
python test_coordinate_extraction.py

# 只运行单元测试
python test_coordinate_extraction.py --unit-tests

# 只运行集成测试
python test_coordinate_extraction.py --integration-tests

# 运行功能演示
python test_coordinate_extraction.py --demo
```

### ✅ 测试覆盖

- **单元测试**：测试各个组件的独立功能
- **集成测试**：测试完整的坐标提取流程
- **性能测试**：验证处理性能和内存使用
- **边界情况测试**：测试异常输入和边界条件

## 性能考虑

### ⚡ 性能特点

- **时间复杂度**：O(n*m)，其中n是测试项目数，m是OCR块数
- **空间复杂度**：O(n+m)，主要用于存储映射关系
- **处理速度**：通常在毫秒级别完成坐标提取

### 🎛️ 性能优化

- 使用高效的文本相似度算法
- 智能的行分组算法减少计算量
- 可配置的阈值参数平衡精度和性能

## 注意事项

### ⚠️ 限制和约束

1. **依赖OCR质量**：坐标提取的准确性依赖于OCR识别的质量
2. **文本匹配精度**：复杂的医疗术语可能影响匹配精度
3. **布局假设**：假设检验报告采用相对规整的行列布局
4. **坐标系统**：使用OCR服务返回的原始坐标系统

### 💡 最佳实践

1. **参数调优**：根据具体的报告格式调整相似度阈值和Y容差
2. **质量检查**：检查匹配置信度，过滤低质量匹配
3. **异常处理**：妥善处理坐标提取失败的情况
4. **性能监控**：在批量处理时监控内存和处理时间

## 扩展可能

### 🚀 未来增强

- **字段级坐标**：支持提取单个字段的精确坐标
- **表格结构识别**：识别和提取表格的行列结构
- **多列布局支持**：更好地处理复杂的多列布局
- **坐标校正**：基于图片旋转和缩放的坐标校正
