# -*- coding: utf-8 -*-
"""
坐标提取功能独立测试脚本

该脚本独立测试坐标提取功能，不依赖Django环境或完整的处理流程。
专门用于验证坐标提取算法的正确性。

使用方法:
    python test_coordinate_standalone.py
"""

import sys
import os
import unittest
import json
from pathlib import Path

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 直接导入坐标提取模块，避免依赖Django
from script.test_result_format_ae_ocr.utils.coordinate_extraction import (
    CoordinateMapper,
    RowCoordinateExtractor,
    RowCoordinate,
    extract_row_coordinates,
    map_items_to_coordinates,
    get_row_y_coordinates,
    get_row_bounding_boxes,
    export_coordinates_to_json
)

# 简化的TestItem类，用于测试
class SimpleTestItem:
    """简化的测试项目类，用于独立测试"""
    def __init__(self, test_code="", test_name="", test_value="", test_unit="", reference_value=""):
        self.test_code = test_code
        self.test_name = test_name
        self.test_value = test_value
        self.test_unit = test_unit
        self.reference_value = reference_value


def create_test_data():
    """创建测试数据"""
    # 模拟测试项目
    test_items = [
        SimpleTestItem(
            test_code="GLU",
            test_name="葡萄糖",
            test_value="4.90",
            test_unit="mmol/L",
            reference_value="3.90-6.10"
        ),
        SimpleTestItem(
            test_code="CHOL",
            test_name="胆固醇",
            test_value="5.2",
            test_unit="mmol/L",
            reference_value="3.0-5.0"
        ),
        SimpleTestItem(
            test_code="TG",
            test_name="甘油三酯",
            test_value="1.8",
            test_unit="mmol/L",
            reference_value="0.5-1.7"
        )
    ]
    
    # 模拟OCR文本块（三行数据）
    ocr_blocks = [
        # 第一行：葡萄糖
        {
            "words": "GLU",
            "location": [[50, 200], [80, 200], [80, 220], [50, 220]],
            "confidence": 0.95
        },
        {
            "words": "葡萄糖",
            "location": [[100, 200], [150, 200], [150, 220], [100, 220]],
            "confidence": 0.95
        },
        {
            "words": "4.90",
            "location": [[200, 205], [230, 205], [230, 225], [200, 225]],
            "confidence": 0.98
        },
        {
            "words": "mmol/L",
            "location": [[300, 200], [350, 200], [350, 220], [300, 220]],
            "confidence": 0.90
        },
        {
            "words": "3.90-6.10",
            "location": [[400, 200], [470, 200], [470, 220], [400, 220]],
            "confidence": 0.92
        },
        
        # 第二行：胆固醇
        {
            "words": "CHOL",
            "location": [[50, 250], [85, 250], [85, 270], [50, 270]],
            "confidence": 0.93
        },
        {
            "words": "胆固醇",
            "location": [[100, 250], [150, 250], [150, 270], [100, 270]],
            "confidence": 0.93
        },
        {
            "words": "5.2",
            "location": [[200, 255], [220, 255], [220, 275], [200, 275]],
            "confidence": 0.97
        },
        {
            "words": "mmol/L",
            "location": [[300, 250], [350, 250], [350, 270], [300, 270]],
            "confidence": 0.90
        },
        {
            "words": "3.0-5.0",
            "location": [[400, 250], [450, 250], [450, 270], [400, 270]],
            "confidence": 0.91
        },
        
        # 第三行：甘油三酯
        {
            "words": "TG",
            "location": [[50, 300], [70, 300], [70, 320], [50, 320]],
            "confidence": 0.94
        },
        {
            "words": "甘油三酯",
            "location": [[100, 300], [160, 300], [160, 320], [100, 320]],
            "confidence": 0.96
        },
        {
            "words": "1.8",
            "location": [[200, 305], [220, 305], [220, 325], [200, 325]],
            "confidence": 0.99
        },
        {
            "words": "mmol/L",
            "location": [[300, 300], [350, 300], [350, 320], [300, 320]],
            "confidence": 0.90
        },
        {
            "words": "0.5-1.7",
            "location": [[400, 300], [440, 300], [440, 320], [400, 320]],
            "confidence": 0.88
        }
    ]
    
    return test_items, ocr_blocks


def test_coordinate_mapper():
    """测试坐标映射器"""
    print("🧪 测试坐标映射器...")
    
    test_items, ocr_blocks = create_test_data()
    mapper = CoordinateMapper(similarity_threshold=0.6)
    
    # 测试文本相似度
    similarity = mapper.calculate_text_similarity("葡萄糖", "葡萄糖")
    print(f"  文本相似度测试: {similarity:.2f} (期望: 1.0)")
    assert similarity == 1.0, "完全匹配的文本相似度应该为1.0"
    
    # 测试查找匹配块
    matches = mapper.find_matching_blocks("葡萄糖", ocr_blocks)
    print(f"  匹配块查找: 找到 {len(matches)} 个匹配")
    assert len(matches) > 0, "应该找到匹配的OCR块"
    
    # 测试项目到块的映射
    matched_blocks = mapper.map_item_to_blocks(test_items[0], ocr_blocks)
    print(f"  项目映射: 测试项目0映射到 {len(matched_blocks)} 个OCR块")
    assert len(matched_blocks) > 0, "应该找到匹配的OCR块"
    
    print("  ✅ 坐标映射器测试通过")


def test_row_extractor():
    """测试行坐标提取器"""
    print("\n🧪 测试行坐标提取器...")
    
    test_items, ocr_blocks = create_test_data()
    extractor = RowCoordinateExtractor(y_tolerance=15.0)
    
    # 测试中心Y坐标计算
    location = [[100, 200], [150, 200], [150, 220], [100, 220]]
    center_y = extractor.get_block_center_y(location)
    print(f"  中心Y坐标: {center_y} (期望: 210.0)")
    assert center_y == 210.0, "中心Y坐标计算错误"
    
    # 测试边界计算
    x_min, y_min, x_max, y_max = extractor.get_block_bounds(location)
    print(f"  边界坐标: ({x_min}, {y_min}) - ({x_max}, {y_max})")
    assert (x_min, y_min, x_max, y_max) == (100.0, 200.0, 150.0, 220.0), "边界坐标计算错误"
    
    # 测试按行分组
    block_indices = list(range(len(ocr_blocks)))
    rows = extractor.group_blocks_by_row(block_indices, ocr_blocks)
    print(f"  行分组: 识别到 {len(rows)} 行")
    assert len(rows) == 3, f"应该识别到3行，实际识别到{len(rows)}行"
    
    print("  ✅ 行坐标提取器测试通过")


def test_integration():
    """测试完整的坐标提取流程"""
    print("\n🧪 测试完整坐标提取流程...")
    
    test_items, ocr_blocks = create_test_data()
    
    # 执行坐标提取
    row_coordinates = extract_row_coordinates(test_items, ocr_blocks)
    
    print(f"  坐标提取结果: {len(row_coordinates)} 行")
    assert len(row_coordinates) > 0, "应该提取到行坐标"
    
    # 验证行按Y坐标排序
    for i in range(1, len(row_coordinates)):
        assert row_coordinates[i-1].center_y <= row_coordinates[i].center_y, "行应该按Y坐标排序"
    
    # 测试工具函数
    y_coords = get_row_y_coordinates(row_coordinates)
    bounding_boxes = get_row_bounding_boxes(row_coordinates)
    
    print(f"  Y坐标提取: {len(y_coords)} 个范围")
    print(f"  边界框提取: {len(bounding_boxes)} 个边界框")
    
    assert len(y_coords) == len(row_coordinates), "Y坐标数量应该等于行数"
    assert len(bounding_boxes) == len(row_coordinates), "边界框数量应该等于行数"
    
    print("  ✅ 完整流程测试通过")


def demo_coordinate_extraction():
    """演示坐标提取功能"""
    print("\n🎯 坐标提取功能演示...")
    print("=" * 60)
    
    test_items, ocr_blocks = create_test_data()
    
    print("📝 测试数据:")
    print(f"  测试项目数: {len(test_items)}")
    for i, item in enumerate(test_items):
        print(f"    {i}: {item.test_name} = {item.test_value}")
    
    print(f"  OCR文本块数: {len(ocr_blocks)}")
    for i, block in enumerate(ocr_blocks):
        location = block["location"]
        center_y = sum(point[1] for point in location) / len(location)
        print(f"    {i}: '{block['words']}' @ Y={center_y:.1f}")
    
    # 执行坐标提取
    print("\n🔍 执行坐标提取...")
    row_coordinates = extract_row_coordinates(test_items, ocr_blocks)
    
    print(f"✅ 提取完成，识别到 {len(row_coordinates)} 行")
    
    # 显示详细结果
    print("\n📍 行坐标详情:")
    print("-" * 60)
    for i, row in enumerate(row_coordinates):
        print(f"行 {i}:")
        print(f"  Y坐标范围: {row.y_min:.1f} - {row.y_max:.1f} (中心: {row.center_y:.1f})")
        print(f"  X坐标范围: {row.x_min:.1f} - {row.x_max:.1f}")
        print(f"  尺寸: {row.width:.1f} x {row.height:.1f}")
        print(f"  置信度: {row.confidence:.2f}")
        print(f"  匹配块数: {len(row.matched_blocks)}")
        print(f"  关联项目: {row.test_item_indices}")
        
        # 显示匹配的文本
        matched_texts = [block["words"] for block in row.matched_blocks]
        print(f"  匹配文本: {', '.join(matched_texts)}")
        print()
    
    # 显示Y坐标列表
    y_coordinates = get_row_y_coordinates(row_coordinates)
    print("📏 Y坐标范围列表:")
    for i, (y_min, y_max) in enumerate(y_coordinates):
        print(f"  行 {i}: {y_min:.1f} - {y_max:.1f}")
    
    # 显示边界框
    bounding_boxes = get_row_bounding_boxes(row_coordinates)
    print("\n📦 边界框列表:")
    for i, (x_min, y_min, x_max, y_max) in enumerate(bounding_boxes):
        print(f"  行 {i}: ({x_min:.1f}, {y_min:.1f}) - ({x_max:.1f}, {y_max:.1f})")
    
    # 导出JSON示例
    json_data = export_coordinates_to_json(row_coordinates, test_items)
    print(f"\n📄 JSON导出示例（前500字符）:")
    json_str = json.dumps(json_data, ensure_ascii=False, indent=2)
    print(json_str[:500] + "..." if len(json_str) > 500 else json_str)


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始坐标提取功能测试")
    print("=" * 60)
    
    try:
        # 运行各项测试
        test_coordinate_mapper()
        test_row_extractor()
        test_integration()
        
        print("\n✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="坐标提取功能独立测试")
    parser.add_argument("--demo", action="store_true", help="运行功能演示")
    parser.add_argument("--tests", action="store_true", help="运行测试")
    
    args = parser.parse_args()
    
    if args.demo:
        demo_coordinate_extraction()
    elif args.tests:
        success = run_all_tests()
        if not success:
            sys.exit(1)
    else:
        # 默认运行测试和演示
        print("🔧 运行测试...")
        success = run_all_tests()
        
        if success:
            print("\n" + "=" * 60)
            demo_coordinate_extraction()
        else:
            sys.exit(1)


if __name__ == "__main__":
    main()
