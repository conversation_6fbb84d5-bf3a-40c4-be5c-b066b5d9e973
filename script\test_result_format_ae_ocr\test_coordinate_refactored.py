# -*- coding: utf-8 -*-
"""
重构后的坐标提取功能测试脚本

该脚本测试重构后的坐标提取功能，包括：
1. 新的OCR数据结构适配
2. 基于line_break的行分组逻辑
3. 四个坐标点的坐标格式
4. 向后兼容性测试

使用方法:
    python test_coordinate_refactored.py
"""

import sys
import os
from pathlib import Path

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 直接导入坐标提取模块的核心功能
from script.test_result_format_ae_ocr.utils.coordinate_extraction import (
    CoordinateMapper,
    RowCoordinateExtractor,
    RowCoordinate,
    extract_row_coordinates
)

# 简化的TestItem类，用于测试
class SimpleTestItem:
    """简化的测试项目类，用于独立测试"""
    def __init__(self, test_code="", test_name="", test_value="", test_unit="", reference_value=""):
        self.test_code = test_code
        self.test_name = test_name
        self.test_value = test_value
        self.test_unit = test_unit
        self.reference_value = reference_value
        self.row_coordinates = None  # 新的坐标格式


def create_new_ocr_data():
    """创建新格式的OCR测试数据"""
    # 模拟测试项目
    test_items = [
        SimpleTestItem(
            test_code="GLU",
            test_name="葡萄糖",
            test_value="4.90",
            test_unit="mmol/L",
            reference_value="3.90-6.10"
        ),
        SimpleTestItem(
            test_code="CHOL",
            test_name="胆固醇",
            test_value="5.2",
            test_unit="mmol/L",
            reference_value="3.0-5.0"
        ),
        SimpleTestItem(
            test_code="TG",
            test_name="甘油三酯",
            test_value="1.8",
            test_unit="mmol/L",
            reference_value="0.5-1.7"
        )
    ]
    
    # 新格式的OCR数据：[{"page": int, "words_block_list": [...]}]
    ocr_data = [
        {
            "page": 1,
            "words_block_list": [
                # 第一行：葡萄糖
                {
                    "words": "GLU",
                    "location": [[50, 200], [80, 200], [80, 220], [50, 220]],
                    "confidence": 0.95
                },
                {
                    "words": "葡萄糖",
                    "location": [[100, 200], [150, 200], [150, 220], [100, 220]],
                    "confidence": 0.95
                },
                {
                    "words": "4.90",
                    "location": [[200, 205], [230, 205], [230, 225], [200, 225]],
                    "confidence": 0.98
                },
                {
                    "words": "mmol/L",
                    "location": [[300, 200], [350, 200], [350, 220], [300, 220]],
                    "confidence": 0.90
                },
                {
                    "words": "3.90-6.10",
                    "location": [[400, 200], [470, 200], [470, 220], [400, 220]],
                    "confidence": 0.92,
                    "line_break": True  # 第一行结束标记
                },
                
                # 第二行：胆固醇
                {
                    "words": "CHOL",
                    "location": [[50, 250], [85, 250], [85, 270], [50, 270]],
                    "confidence": 0.93
                },
                {
                    "words": "胆固醇",
                    "location": [[100, 250], [150, 250], [150, 270], [100, 270]],
                    "confidence": 0.93
                },
                {
                    "words": "5.2",
                    "location": [[200, 255], [220, 255], [220, 275], [200, 275]],
                    "confidence": 0.97
                },
                {
                    "words": "mmol/L",
                    "location": [[300, 250], [350, 250], [350, 270], [300, 270]],
                    "confidence": 0.90
                },
                {
                    "words": "3.0-5.0",
                    "location": [[400, 250], [450, 250], [450, 270], [400, 270]],
                    "confidence": 0.91,
                    "line_break": True  # 第二行结束标记
                },
                
                # 第三行：甘油三酯
                {
                    "words": "TG",
                    "location": [[50, 300], [70, 300], [70, 320], [50, 320]],
                    "confidence": 0.94
                },
                {
                    "words": "甘油三酯",
                    "location": [[100, 300], [160, 300], [160, 320], [100, 320]],
                    "confidence": 0.96
                },
                {
                    "words": "1.8",
                    "location": [[200, 305], [220, 305], [220, 325], [200, 325]],
                    "confidence": 0.99
                },
                {
                    "words": "mmol/L",
                    "location": [[300, 300], [350, 300], [350, 320], [300, 320]],
                    "confidence": 0.90
                },
                {
                    "words": "0.5-1.7",
                    "location": [[400, 300], [440, 300], [440, 320], [400, 320]],
                    "confidence": 0.88,
                    "line_break": True  # 第三行结束标记
                }
            ]
        }
    ]
    
    return test_items, ocr_data


def test_line_break_grouping():
    """测试基于line_break的行分组功能"""
    print("🧪 测试基于line_break的行分组功能...")
    
    test_items, ocr_data = create_new_ocr_data()
    
    # 展开OCR块
    all_blocks = []
    for page_data in ocr_data:
        all_blocks.extend(page_data["words_block_list"])
    
    # 测试行分组
    extractor = RowCoordinateExtractor()
    block_indices = list(range(len(all_blocks)))
    row_groups = extractor.group_blocks_by_line_break(block_indices, all_blocks)
    
    print(f"  识别到 {len(row_groups)} 行")
    assert len(row_groups) == 3, f"应该识别到3行，实际识别到{len(row_groups)}行"
    
    # 验证每行的块数
    expected_blocks_per_row = [5, 5, 5]  # 每行5个块
    for i, row_blocks in enumerate(row_groups):
        print(f"  行 {i}: {len(row_blocks)} 个块")
        assert len(row_blocks) == expected_blocks_per_row[i], f"行{i}应该有{expected_blocks_per_row[i]}个块"
    
    print("  ✅ line_break行分组测试通过")


def test_coordinate_format():
    """测试新的坐标格式"""
    print("\n🧪 测试新的坐标格式...")
    
    test_items, ocr_data = create_new_ocr_data()
    
    # 执行坐标提取
    row_coordinates = extract_row_coordinates(test_items, ocr_data)
    
    print(f"  提取到 {len(row_coordinates)} 行坐标")
    assert len(row_coordinates) > 0, "应该提取到行坐标"
    
    # 测试坐标点计算
    extractor = RowCoordinateExtractor()
    for i, row_coord in enumerate(row_coordinates):
        location_points = extractor.calculate_row_location_points(row_coord)
        
        print(f"  行 {i} 坐标点: {location_points}")
        
        # 验证坐标点格式
        assert len(location_points) == 4, "应该有4个坐标点"
        assert all(len(point) == 2 for point in location_points), "每个坐标点应该有2个值"
        
        # 验证坐标点顺序：左上、右上、右下、左下
        left_top, right_top, right_bottom, left_bottom = location_points
        assert left_top[0] <= right_top[0], "左上X应该小于等于右上X"
        assert left_top[1] <= left_bottom[1], "左上Y应该小于等于左下Y"
        assert right_top[1] <= right_bottom[1], "右上Y应该小于等于右下Y"
    
    print("  ✅ 坐标格式测试通过")


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🧪 测试向后兼容性...")
    
    # 测试不传入OCR数据的情况
    test_items, _ = create_new_ocr_data()
    
    # 不传入OCR数据，应该返回空列表
    row_coordinates = extract_row_coordinates(test_items, [])
    
    print(f"  无OCR数据时提取结果: {len(row_coordinates)} 行")
    assert len(row_coordinates) == 0, "无OCR数据时应该返回空列表"
    
    print("  ✅ 向后兼容性测试通过")


def test_integration():
    """测试完整的集成功能"""
    print("\n🧪 测试完整的集成功能...")
    
    test_items, ocr_data = create_new_ocr_data()
    
    # 执行完整的坐标提取流程
    row_coordinates = extract_row_coordinates(test_items, ocr_data)
    
    print(f"  集成测试结果: {len(row_coordinates)} 行坐标")
    assert len(row_coordinates) == 3, f"应该提取到3行，实际提取到{len(row_coordinates)}行"
    
    # 验证每行都有关联的测试项目
    for i, row in enumerate(row_coordinates):
        print(f"  行 {i}: 关联项目 {row.test_item_indices}, 置信度 {row.confidence:.2f}")
        assert len(row.test_item_indices) > 0, f"行{i}应该有关联的测试项目"
    
    # 验证行按Y坐标排序
    for i in range(1, len(row_coordinates)):
        assert row_coordinates[i-1].center_y <= row_coordinates[i].center_y, "行应该按Y坐标排序"
    
    print("  ✅ 集成测试通过")


def demo_new_features():
    """演示新功能"""
    print("\n🎯 新功能演示...")
    print("=" * 60)
    
    test_items, ocr_data = create_new_ocr_data()
    
    print("📝 测试数据:")
    print(f"  测试项目数: {len(test_items)}")
    for i, item in enumerate(test_items):
        print(f"    {i}: {item.test_name} = {item.test_value}")
    
    print(f"  OCR页数: {len(ocr_data)}")
    total_blocks = sum(len(page['words_block_list']) for page in ocr_data)
    print(f"  总OCR块数: {total_blocks}")
    
    # 显示line_break标记
    print("\n📍 line_break标记:")
    for page_data in ocr_data:
        for i, block in enumerate(page_data['words_block_list']):
            if block.get('line_break', False):
                print(f"    块 {i}: '{block['words']}' [行结束]")
    
    # 执行坐标提取
    print("\n🔍 执行坐标提取...")
    row_coordinates = extract_row_coordinates(test_items, ocr_data)
    
    print(f"✅ 提取完成，识别到 {len(row_coordinates)} 行")
    
    # 显示详细结果
    print("\n📍 行坐标详情:")
    print("-" * 60)
    extractor = RowCoordinateExtractor()
    
    for i, row in enumerate(row_coordinates):
        print(f"行 {i}:")
        print(f"  Y坐标范围: {row.y_min:.1f} - {row.y_max:.1f} (中心: {row.center_y:.1f})")
        print(f"  X坐标范围: {row.x_min:.1f} - {row.x_max:.1f}")
        print(f"  尺寸: {row.width:.1f} x {row.height:.1f}")
        print(f"  置信度: {row.confidence:.2f}")
        print(f"  关联项目: {row.test_item_indices}")
        
        # 显示四个坐标点
        location_points = extractor.calculate_row_location_points(row)
        print(f"  坐标点: {location_points}")
        
        # 显示匹配的文本
        matched_texts = [block["words"] for block in row.matched_blocks]
        print(f"  匹配文本: {', '.join(matched_texts)}")
        print()
    
    print("🎯 新功能演示完成！")


def main():
    """主函数"""
    print("🚀 重构后的坐标提取功能测试")
    print("=" * 60)
    
    try:
        # 运行各项测试
        test_line_break_grouping()
        test_coordinate_format()
        test_backward_compatibility()
        test_integration()
        
        print("\n✅ 所有测试通过！")
        
        # 运行演示
        demo_new_features()
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
