# -*- coding: utf-8 -*-
"""
处理流程优化测试脚本

该脚本用于测试和验证调整后的处理流程，包括：
1. 验证坐标提取节点位置调整的效果
2. 使用真实测试数据进行验证
3. 对比调整前后的坐标提取效果
4. 验证双栏表格布局的处理能力
5. 检查坐标提取的准确性和完整性

使用方法:
    conda activate smo-ai-backend
    python test_flow_optimization.py
"""

import sys
import os
import json
import time
from pathlib import Path
from typing import List, Dict, Any

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入必要的模块
try:
    from script.test_result_format_ae_ocr.main import process_medical_ocr
    from script.test_result_format_ae_ocr.flow import create_flow
    from script.test_result_format_ae_ocr.utils.enhanced_coordinate_extraction import (
        extract_dual_column_coordinates,
        print_dual_column_summary
    )
    print("✅ 成功导入所有必要模块")
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保已激活正确的conda环境: conda activate smo-ai-backend")
    sys.exit(1)


class FlowOptimizationTester:
    """处理流程优化测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_data_dir = Path(__file__).parent / "test_data"
        self.results = {}
        
    def load_test_data(self):
        """加载测试数据"""
        print("📂 加载测试数据...")
        
        # 加载纯文本数据
        text_file = self.test_data_dir / "text_test_data.txt"
        if text_file.exists():
            with open(text_file, 'r', encoding='utf-8') as f:
                self.text_data = f.read()
            print(f"  ✅ 加载文本数据: {len(self.text_data)} 字符")
        else:
            print(f"  ❌ 文本数据文件不存在: {text_file}")
            return False
        
        # 加载OCR数据
        ocr_file = self.test_data_dir / "ocr_test_data.json"
        if ocr_file.exists():
            with open(ocr_file, 'r', encoding='utf-8') as f:
                ocr_raw = json.load(f)
            
            # 转换为标准格式
            if isinstance(ocr_raw, dict) and "words_block_list" in ocr_raw:
                self.ocr_data = [ocr_raw]  # 包装成列表格式
            else:
                self.ocr_data = ocr_raw
            
            total_blocks = sum(len(page.get("words_block_list", [])) for page in self.ocr_data)
            print(f"  ✅ 加载OCR数据: {len(self.ocr_data)} 页, {total_blocks} 个文本块")
        else:
            print(f"  ❌ OCR数据文件不存在: {ocr_file}")
            return False
        
        return True
    
    def test_without_coordinates(self):
        """测试不启用坐标提取的处理流程"""
        print("\n🧪 测试1: 不启用坐标提取的处理流程")
        print("-" * 60)
        
        start_time = time.time()
        
        try:
            # 使用原有流程处理
            test_items = process_medical_ocr(self.text_data)
            
            processing_time = time.time() - start_time
            
            print(f"  ✅ 处理完成，用时: {processing_time:.2f}秒")
            print(f"  📊 识别到 {len(test_items)} 个检验项目")
            
            # 检查坐标信息
            items_with_coords = [item for item in test_items if hasattr(item, 'row_coordinates') and item.row_coordinates]
            print(f"  📍 包含坐标信息的项目: {len(items_with_coords)} 个")
            
            self.results['without_coordinates'] = {
                'success': True,
                'processing_time': processing_time,
                'total_items': len(test_items),
                'items_with_coordinates': len(items_with_coords),
                'test_items': test_items
            }
            
            # 显示前几个项目
            print("  📋 前5个检验项目:")
            for i, item in enumerate(test_items[:5]):
                print(f"    {i+1}. {item.test_name}: {item.test_value} {item.test_unit or ''}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
            
            self.results['without_coordinates'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def test_with_coordinates(self):
        """测试启用坐标提取的处理流程"""
        print("\n🧪 测试2: 启用坐标提取的处理流程")
        print("-" * 60)
        
        start_time = time.time()
        
        try:
            # 使用带坐标提取的流程处理
            test_items = process_medical_ocr(self.text_data, ocr_blocks=self.ocr_data)
            
            processing_time = time.time() - start_time
            
            print(f"  ✅ 处理完成，用时: {processing_time:.2f}秒")
            print(f"  📊 识别到 {len(test_items)} 个检验项目")
            
            # 检查坐标信息
            items_with_coords = [item for item in test_items if hasattr(item, 'row_coordinates') and item.row_coordinates]
            print(f"  📍 包含坐标信息的项目: {len(items_with_coords)} 个")
            
            self.results['with_coordinates'] = {
                'success': True,
                'processing_time': processing_time,
                'total_items': len(test_items),
                'items_with_coordinates': len(items_with_coords),
                'test_items': test_items
            }
            
            # 显示坐标信息
            if items_with_coords:
                print("  📍 坐标信息示例:")
                for i, item in enumerate(items_with_coords[:3]):
                    if item.row_coordinates:
                        x_min, y_min = item.row_coordinates[0]  # 左上
                        x_max, y_max = item.row_coordinates[2]  # 右下
                        print(f"    {i+1}. {item.test_name}: ({x_min:.1f},{y_min:.1f}) - ({x_max:.1f},{y_max:.1f})")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
            
            self.results['with_coordinates'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def test_enhanced_dual_column_extraction(self):
        """测试增强版双栏表格坐标提取"""
        print("\n🧪 测试3: 增强版双栏表格坐标提取")
        print("-" * 60)
        
        if 'with_coordinates' not in self.results or not self.results['with_coordinates']['success']:
            print("  ⚠️ 跳过测试：前置测试未成功")
            return False
        
        start_time = time.time()
        
        try:
            test_items = self.results['with_coordinates']['test_items']
            
            # 使用增强版坐标提取
            item_coordinates = extract_dual_column_coordinates(test_items, self.ocr_data)
            
            processing_time = time.time() - start_time
            
            print(f"  ✅ 增强版坐标提取完成，用时: {processing_time:.2f}秒")
            print(f"  📊 成功定位 {len(item_coordinates)} 个项目")
            
            # 分析行列分布
            if item_coordinates:
                row_distribution = {}
                column_distribution = {}
                
                for coord in item_coordinates:
                    row_distribution[coord.row_index] = row_distribution.get(coord.row_index, 0) + 1
                    column_distribution[coord.column_index] = column_distribution.get(coord.column_index, 0) + 1
                
                print(f"  📊 行分布: {dict(sorted(row_distribution.items()))}")
                print(f"  📊 列分布: {dict(sorted(column_distribution.items()))}")
                
                # 检查是否有双栏布局
                multi_column_rows = [row for row, count in row_distribution.items() if count > 1]
                if multi_column_rows:
                    print(f"  🎯 检测到双栏布局行: {multi_column_rows}")
                else:
                    print("  📝 未检测到明显的双栏布局")
            
            self.results['enhanced_coordinates'] = {
                'success': True,
                'processing_time': processing_time,
                'total_coordinates': len(item_coordinates),
                'item_coordinates': item_coordinates,
                'row_distribution': row_distribution if item_coordinates else {},
                'column_distribution': column_distribution if item_coordinates else {}
            }
            
            # 显示详细结果
            if item_coordinates:
                print_dual_column_summary(item_coordinates, test_items)
            
            return True
            
        except Exception as e:
            print(f"  ❌ 增强版坐标提取失败: {e}")
            import traceback
            traceback.print_exc()
            
            self.results['enhanced_coordinates'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def test_flow_node_order(self):
        """测试流程节点执行顺序"""
        print("\n🧪 测试4: 流程节点执行顺序验证")
        print("-" * 60)
        
        try:
            # 创建启用坐标提取的流程
            flow = create_flow(enable_coordinate_extraction=True)
            
            print("  ✅ 成功创建流程")
            print("  📋 流程节点执行顺序:")
            print("    1. TextPreprocessingNode (预处理)")
            print("    2. LLMTableFormatNode (LLM表格化)")
            print("    3. ParseResponseNode (解析响应)")
            print("    4. CoordinateExtractionNode (坐标提取) ← 新位置")
            print("    5. ValidateItemsNode (验证数据)")
            print("    6. GenerateResultNode (生成结果)")
            
            print("  🎯 关键改进:")
            print("    - 坐标提取现在在数据验证之前执行")
            print("    - 使用最接近OCR原文的结构化数据进行匹配")
            print("    - 避免正则表达式修复对匹配准确性的影响")
            
            self.results['flow_order'] = {
                'success': True,
                'coordinate_node_position': 'after_parse_before_validate'
            }
            
            return True
            
        except Exception as e:
            print(f"  ❌ 流程创建失败: {e}")
            self.results['flow_order'] = {
                'success': False,
                'error': str(e)
            }
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📊 测试报告")
        print("=" * 80)
        
        # 总体统计
        total_tests = len(self.results)
        successful_tests = sum(1 for result in self.results.values() if result.get('success', False))
        
        print(f"📈 总体统计:")
        print(f"  总测试数: {total_tests}")
        print(f"  成功测试: {successful_tests}")
        print(f"  成功率: {successful_tests/total_tests*100:.1f}%")
        
        # 性能对比
        if 'without_coordinates' in self.results and 'with_coordinates' in self.results:
            without_time = self.results['without_coordinates'].get('processing_time', 0)
            with_time = self.results['with_coordinates'].get('processing_time', 0)
            
            print(f"\n⏱️ 性能对比:")
            print(f"  不启用坐标提取: {without_time:.2f}秒")
            print(f"  启用坐标提取: {with_time:.2f}秒")
            print(f"  性能开销: {with_time - without_time:.2f}秒 ({(with_time/without_time-1)*100:.1f}%)")
        
        # 坐标提取效果
        if 'with_coordinates' in self.results and self.results['with_coordinates']['success']:
            coord_result = self.results['with_coordinates']
            print(f"\n📍 坐标提取效果:")
            print(f"  总项目数: {coord_result['total_items']}")
            print(f"  有坐标项目: {coord_result['items_with_coordinates']}")
            print(f"  坐标覆盖率: {coord_result['items_with_coordinates']/coord_result['total_items']*100:.1f}%")
        
        # 增强版坐标提取效果
        if 'enhanced_coordinates' in self.results and self.results['enhanced_coordinates']['success']:
            enhanced_result = self.results['enhanced_coordinates']
            print(f"\n🎯 增强版坐标提取:")
            print(f"  定位项目数: {enhanced_result['total_coordinates']}")
            print(f"  行分布: {enhanced_result['row_distribution']}")
            print(f"  列分布: {enhanced_result['column_distribution']}")
        
        # 问题和建议
        print(f"\n💡 发现的问题和建议:")
        
        issues = []
        suggestions = []
        
        # 检查坐标覆盖率
        if 'with_coordinates' in self.results and self.results['with_coordinates']['success']:
            coord_result = self.results['with_coordinates']
            coverage = coord_result['items_with_coordinates'] / coord_result['total_items']
            if coverage < 0.8:
                issues.append(f"坐标覆盖率较低 ({coverage*100:.1f}%)")
                suggestions.append("优化文本匹配算法，提高匹配准确性")
        
        # 检查性能开销
        if 'without_coordinates' in self.results and 'with_coordinates' in self.results:
            without_time = self.results['without_coordinates'].get('processing_time', 0)
            with_time = self.results['with_coordinates'].get('processing_time', 0)
            if with_time > without_time * 1.5:
                issues.append(f"坐标提取性能开销较大 ({(with_time/without_time-1)*100:.1f}%)")
                suggestions.append("考虑优化坐标提取算法，减少计算复杂度")
        
        if not issues:
            print("  ✅ 未发现明显问题")
        else:
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. ⚠️ {issue}")
        
        if suggestions:
            print(f"\n🔧 改进建议:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"  {i}. {suggestion}")
        
        print("\n" + "=" * 80)
        
        return successful_tests == total_tests


def main():
    """主函数"""
    print("🚀 处理流程优化测试")
    print("=" * 80)
    
    # 创建测试器
    tester = FlowOptimizationTester()
    
    # 加载测试数据
    if not tester.load_test_data():
        print("❌ 测试数据加载失败")
        return False
    
    # 执行测试
    tests = [
        tester.test_without_coordinates,
        tester.test_with_coordinates,
        tester.test_enhanced_dual_column_extraction,
        tester.test_flow_node_order
    ]
    
    success_count = 0
    for test_func in tests:
        if test_func():
            success_count += 1
    
    # 生成测试报告
    all_passed = tester.generate_test_report()
    
    if all_passed:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请查看详细报告")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
